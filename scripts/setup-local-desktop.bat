@echo off
REM Setup script for Facebook Automation Desktop Application with Docker services
REM Sets up backend virtual environment and frontend dependencies

setlocal enabledelayedexpansion

echo 🚀 Setting up Facebook Automation Desktop Application for local development with Docker services...

REM Configuration
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..
set DOCKER_COMPOSE_DIR=%PROJECT_DIR%\auto-login

REM Function to print colored output (simulated)
:print_status
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Check if Docker is installed
:check_docker
call :print_status "Checking Docker installation..."

docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not installed"
    call :print_status "Please install Docker: https://docs.docker.com/get-docker/"
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose is not installed"
    call :print_status "Please install Docker Compose: https://docs.docker.com/compose/install/"
    exit /b 1
)

REM Check if Docker daemon is running
docker info >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker daemon is not running"
    call :print_status "Please start Docker daemon"
    exit /b 1
)

call :print_success "Docker is installed and running"
call :print_success "Docker Compose is installed"
goto :eof

REM Check if Python is installed
:check_python
call :print_status "Checking Python installation..."

python --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Python is not installed. Please install Python 3.8+ from https://python.org/"
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
for /f "tokens=1,2 delims=." %%a in ("%PYTHON_VERSION%") do (
    set PYTHON_MAJOR=%%a
    set PYTHON_MINOR=%%b
)

if %PYTHON_MAJOR% LSS 3 (
    call :print_error "Python 3.8+ is required. Current version: %PYTHON_VERSION%"
    exit /b 1
)

if %PYTHON_MAJOR% EQU 3 if %PYTHON_MINOR% LSS 8 (
    call :print_error "Python 3.8+ is required. Current version: %PYTHON_VERSION%"
    exit /b 1
)

call :print_success "Python %PYTHON_VERSION% is installed"
goto :eof

REM Check if Node.js is installed
:check_nodejs
call :print_status "Checking Node.js installation..."

node --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/"
    exit /b 1
)

for /f "tokens=1 delims=v" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=1 delims=." %%a in ("%NODE_VERSION%") do set NODE_MAJOR=%%a

if %NODE_MAJOR% LSS 16 (
    call :print_error "Node.js version 16+ is required. Current version: v%NODE_VERSION%"
    exit /b 1
)

call :print_success "Node.js v%NODE_VERSION% is installed"
goto :eof

REM Setup backend virtual environment
:setup_backend
call :print_status "Setting up backend environment..."

cd /d "%PROJECT_DIR%\backend"

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    call :print_status "Creating Python virtual environment..."
    python -m venv venv
    call :print_success "Virtual environment created"
) else (
    call :print_status "Virtual environment already exists"
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Upgrade pip
call :print_status "Upgrading pip..."
python -m pip install --upgrade pip

REM Install requirements
call :print_status "Installing Python dependencies..."
if exist "requirements.txt" (
    pip install -r requirements.txt
    call :print_success "Backend dependencies installed"
) else (
    call :print_error "requirements.txt not found"
    exit /b 1
)

REM Check if Camoufox is available
if exist "..\camoufox\pythonlib" (
    call :print_status "Installing Camoufox antidetect browser..."
    pip install -e ..\camoufox\pythonlib
    call :print_success "Camoufox installed"
) else (
    call :print_warning "Camoufox not found, skipping antidetect browser setup"
)

call :print_success "Backend setup completed"
goto :eof

REM Setup frontend dependencies
:setup_frontend
call :print_status "Setting up frontend environment..."

cd /d "%PROJECT_DIR%\frontend"

REM Check if package.json exists
if not exist "package.json" (
    call :print_error "Frontend package.json not found"
    exit /b 1
)

REM Install npm dependencies
call :print_status "Installing Node.js dependencies..."
npm install

call :print_success "Frontend dependencies installed"
call :print_success "Frontend setup completed"
goto :eof

REM Setup Docker environment
:setup_docker_env
call :print_status "Setting up Docker environment..."

cd /d "%DOCKER_COMPOSE_DIR%"

REM Create .env.product if it doesn't exist
if not exist ".env.product" (
    call :print_status "Creating Docker environment file..."
    (
        echo # Database Configuration
        echo DB_HOST=localhost
        echo DB_PORT=5432
        echo DB_USERNAME=postgres
        echo DB_PASSWORD=postgres123
        echo DB_DATABASE=facebook_automation
        echo.
        echo # Redis Configuration
        echo REDIS_HOST=localhost
        echo REDIS_PORT=6379
        echo REDIS_PASSWORD=redis123
        echo.
        echo # Application Configuration
        echo NODE_ENV=development
        echo JWT_SECRET=your-jwt-secret-key-here-%RANDOM%
        echo API_PORT=3000
    ) > .env.product
    call :print_success "Created .env.product file"
) else (
    call :print_status ".env.product already exists"
)

REM Test Docker Compose configuration
call :print_status "Testing Docker Compose configuration..."
docker-compose config >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose configuration is invalid"
    exit /b 1
) else (
    call :print_success "Docker Compose configuration is valid"
)

call :print_success "Docker environment setup completed"
goto :eof

REM Test Docker services
:test_docker_services
call :print_status "Testing Docker services..."

cd /d "%DOCKER_COMPOSE_DIR%"

REM Start services for testing
call :print_status "Starting Docker services for testing..."
docker-compose up -d postgres redis

REM Wait for services to be ready
call :print_status "Waiting for services to be ready..."
timeout /t 15 /nobreak >nul

REM Test PostgreSQL connection
call :print_status "Testing PostgreSQL connection..."
docker-compose exec -T postgres pg_isready -U postgres >nul 2>&1
if errorlevel 1 (
    call :print_warning "PostgreSQL connection test failed"
) else (
    call :print_success "PostgreSQL is accessible"
)

REM Test Redis connection
call :print_status "Testing Redis connection..."
docker-compose exec -T redis redis-cli ping | findstr "PONG" >nul 2>&1
if errorlevel 1 (
    call :print_warning "Redis connection test failed"
) else (
    call :print_success "Redis is accessible"
)

REM Stop test services
call :print_status "Stopping test services..."
docker-compose down >nul 2>&1

call :print_success "Docker services test completed"
goto :eof

REM Create necessary directories
:create_directories
call :print_status "Creating necessary directories..."

REM Create data directories
if not exist "%PROJECT_DIR%\data\profiles" mkdir "%PROJECT_DIR%\data\profiles"
if not exist "%PROJECT_DIR%\data\exports" mkdir "%PROJECT_DIR%\data\exports"
if not exist "%PROJECT_DIR%\logs" mkdir "%PROJECT_DIR%\logs"
if not exist "%PROJECT_DIR%\backend\logs" mkdir "%PROJECT_DIR%\backend\logs"
if not exist "%PROJECT_DIR%\frontend\logs" mkdir "%PROJECT_DIR%\frontend\logs"

call :print_success "Directories created"
goto :eof

REM Show setup summary
:show_summary
call :print_success "🎉 Setup completed successfully!"
echo.
call :print_status "Setup Summary:"
echo   ✅ Docker and Docker Compose verified
echo   ✅ Python virtual environment created
echo   ✅ Backend dependencies installed
echo   ✅ Frontend dependencies installed
echo   ✅ Docker environment configured
echo   ✅ Necessary directories created
echo.
call :print_status "Next Steps:"
echo   1. Run the application: .\scripts\run-local-desktop.bat
echo   2. Or run backend only: .\scripts\run-local-desktop.bat --backend-only
echo   3. Or run frontend only: .\scripts\run-local-desktop.bat --frontend-only
echo.
call :print_status "Available Services:"
echo   📡 Backend API: http://localhost:8000
echo   📚 API Documentation: http://localhost:8000/docs
echo   🖥️  Desktop App: Electron application
echo   🐘 PostgreSQL: Docker container (port 5432)
echo   🔴 Redis: Docker container (port 6379)
echo.
goto :eof

REM Show help
:show_help
echo Usage: %0 [OPTIONS]
echo.
echo Setup Facebook Automation Desktop Application for local development
echo.
echo This script:
echo 1. Checks system prerequisites (Docker, Python, Node.js)
echo 2. Sets up Python virtual environment for backend
echo 3. Installs backend dependencies
echo 4. Installs frontend dependencies
echo 5. Configures Docker environment for PostgreSQL and Redis
echo 6. Creates necessary directories
echo 7. Tests Docker services
echo.
echo OPTIONS:
echo   --skip-docker       Skip Docker setup and testing
echo   --skip-backend      Skip backend setup
echo   --skip-frontend     Skip frontend setup
echo   --test-only         Only test existing setup
echo   --help              Show this help message
echo.
echo EXAMPLES:
echo   %0                          # Full setup (recommended)
echo   %0 --skip-docker            # Setup without Docker testing
echo   %0 --test-only              # Test existing setup
echo   %0 --help                   # Show this help
echo.
echo PREREQUISITES:
echo   - Docker and Docker Compose
echo   - Python 3.8+
echo   - Node.js 16+
echo.
goto :eof

REM Main function
:main
set SKIP_DOCKER=false
set SKIP_BACKEND=false
set SKIP_FRONTEND=false
set TEST_ONLY=false

REM Parse arguments
:parse_args
if "%~1"=="" goto :start_setup
if "%~1"=="--skip-docker" (
    set SKIP_DOCKER=true
    shift
    goto :parse_args
)
if "%~1"=="--skip-backend" (
    set SKIP_BACKEND=true
    shift
    goto :parse_args
)
if "%~1"=="--skip-frontend" (
    set SKIP_FRONTEND=true
    shift
    goto :parse_args
)
if "%~1"=="--test-only" (
    set TEST_ONLY=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    call :show_help
    exit /b 0
)
shift
goto :parse_args

:start_setup
call :print_status "Starting setup process..."
call :print_status "Project directory: %PROJECT_DIR%"
call :print_status "Docker Compose directory: %DOCKER_COMPOSE_DIR%"

if "%TEST_ONLY%"=="true" (
    call :print_status "Running tests only..."
    call :check_docker
    if errorlevel 1 exit /b 1
    call :check_python
    if errorlevel 1 exit /b 1
    call :check_nodejs
    if errorlevel 1 exit /b 1
    if not "%SKIP_DOCKER%"=="true" (
        call :test_docker_services
        if errorlevel 1 exit /b 1
    )
    call :print_success "All tests passed!"
    exit /b 0
)

REM Run setup steps
call :check_docker
if errorlevel 1 exit /b 1

call :check_python
if errorlevel 1 exit /b 1

call :check_nodejs
if errorlevel 1 exit /b 1

if not "%SKIP_DOCKER%"=="true" (
    call :setup_docker_env
    if errorlevel 1 exit /b 1
)

if not "%SKIP_BACKEND%"=="true" (
    call :setup_backend
    if errorlevel 1 exit /b 1
)

if not "%SKIP_FRONTEND%"=="true" (
    call :setup_frontend
    if errorlevel 1 exit /b 1
)

call :create_directories

if not "%SKIP_DOCKER%"=="true" (
    call :test_docker_services
)

call :show_summary

goto :eof

REM Run main function
call :main %*
