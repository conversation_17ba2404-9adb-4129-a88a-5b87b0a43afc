#!/bin/bash

# Setup script for Facebook Automation Desktop Application with Docker services
# Sets up backend virtual environment and frontend dependencies

set -e  # Exit on any error

echo "🚀 Setting up Facebook Automation Desktop Application for local development with Docker services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
DOCKER_COMPOSE_DIR="$PROJECT_DIR/auto-login"

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        print_status "Please install Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        print_status "Please install Docker Compose: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        print_status "Please start Docker daemon"
        exit 1
    fi
    
    print_success "Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1) is installed and running"
    print_success "Docker Compose $(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1) is installed"
}

# Check if Python is installed
check_python() {
    print_status "Checking Python installation..."
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.8+ from https://python.org/"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1-2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
        print_error "Python 3.8+ is required. Current version: $(python3 --version)"
        exit 1
    fi
    
    print_success "Python $(python3 --version | cut -d' ' -f2) is installed"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        print_error "Node.js version 16+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
}

# Setup backend virtual environment
setup_backend() {
    print_status "Setting up backend environment..."
    
    cd "$PROJECT_DIR/backend"
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
        print_success "Virtual environment created"
    else
        print_status "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install requirements
    print_status "Installing Python dependencies..."
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        print_success "Backend dependencies installed"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
    
    # Check if Camoufox is available
    if [ -d "../camoufox/pythonlib" ]; then
        print_status "Installing Camoufox antidetect browser..."
        pip install -e ../camoufox/pythonlib
        print_success "Camoufox installed"
    else
        print_warning "Camoufox not found, skipping antidetect browser setup"
    fi
    
    print_success "Backend setup completed"
}

# Setup frontend dependencies
setup_frontend() {
    print_status "Setting up frontend environment..."
    
    cd "$PROJECT_DIR/frontend"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "Frontend package.json not found"
        exit 1
    fi
    
    # Install npm dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    print_success "Frontend dependencies installed"
    print_success "Frontend setup completed"
}

# Setup Docker environment
setup_docker_env() {
    print_status "Setting up Docker environment..."
    
    cd "$DOCKER_COMPOSE_DIR"
    
    # Create .env.product if it doesn't exist
    if [ ! -f ".env.product" ]; then
        print_status "Creating Docker environment file..."
        cat > .env.product << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres123
DB_DATABASE=facebook_automation

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis123

# Application Configuration
NODE_ENV=development
JWT_SECRET=your-jwt-secret-key-here-$(date +%s)
API_PORT=3000
EOF
        print_success "Created .env.product file"
    else
        print_status ".env.product already exists"
    fi
    
    # Test Docker Compose configuration
    print_status "Testing Docker Compose configuration..."
    if docker-compose config &> /dev/null; then
        print_success "Docker Compose configuration is valid"
    else
        print_error "Docker Compose configuration is invalid"
        exit 1
    fi
    
    print_success "Docker environment setup completed"
}

# Test Docker services
test_docker_services() {
    print_status "Testing Docker services..."

    cd "$DOCKER_COMPOSE_DIR"

    # Start services for testing
    print_status "Starting Docker services for testing..."
    docker-compose up -d postgres redis

    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 15

    # Test PostgreSQL connection
    print_status "Testing PostgreSQL connection..."
    if docker-compose exec -T postgres pg_isready -U postgres &> /dev/null; then
        print_success "PostgreSQL is accessible"
    else
        print_warning "PostgreSQL connection test failed"
    fi

    # Test Redis connection
    print_status "Testing Redis connection..."
    if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
        print_success "Redis is accessible"
    else
        print_warning "Redis connection test failed"
    fi

    # Stop test services
    print_status "Stopping test services..."
    docker-compose down

    print_success "Docker services test completed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."

    # Create data directories
    mkdir -p "$PROJECT_DIR/data/profiles"
    mkdir -p "$PROJECT_DIR/data/exports"
    mkdir -p "$PROJECT_DIR/logs"
    mkdir -p "$PROJECT_DIR/backend/logs"
    mkdir -p "$PROJECT_DIR/frontend/logs"

    print_success "Directories created"
}

# Show setup summary
show_summary() {
    print_success "🎉 Setup completed successfully!"
    echo ""
    print_status "Setup Summary:"
    echo "  ✅ Docker and Docker Compose verified"
    echo "  ✅ Python virtual environment created"
    echo "  ✅ Backend dependencies installed"
    echo "  ✅ Frontend dependencies installed"
    echo "  ✅ Docker environment configured"
    echo "  ✅ Necessary directories created"
    echo ""
    print_status "Next Steps:"
    echo "  1. Run the application: ./scripts/run-local-desktop.sh"
    echo "  2. Or run backend only: ./scripts/run-local-desktop.sh --backend-only"
    echo "  3. Or run frontend only: ./scripts/run-local-desktop.sh --frontend-only"
    echo ""
    print_status "Available Services:"
    echo "  📡 Backend API: http://localhost:8000"
    echo "  📚 API Documentation: http://localhost:8000/docs"
    echo "  🖥️  Desktop App: Electron application"
    echo "  🐘 PostgreSQL: Docker container (port 5432)"
    echo "  🔴 Redis: Docker container (port 6379)"
    echo ""
}

# Show help
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Setup Facebook Automation Desktop Application for local development

This script:
1. Checks system prerequisites (Docker, Python, Node.js)
2. Sets up Python virtual environment for backend
3. Installs backend dependencies
4. Installs frontend dependencies
5. Configures Docker environment for PostgreSQL and Redis
6. Creates necessary directories
7. Tests Docker services

OPTIONS:
  --skip-docker       Skip Docker setup and testing
  --skip-backend      Skip backend setup
  --skip-frontend     Skip frontend setup
  --test-only         Only test existing setup
  --help              Show this help message

EXAMPLES:
  $0                          # Full setup (recommended)
  $0 --skip-docker            # Setup without Docker testing
  $0 --test-only              # Test existing setup
  $0 --help                   # Show this help

PREREQUISITES:
  - Docker and Docker Compose
  - Python 3.8+
  - Node.js 16+

EOF
}

# Main function
main() {
    # Parse arguments
    SKIP_DOCKER=false
    SKIP_BACKEND=false
    SKIP_FRONTEND=false
    TEST_ONLY=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-docker)
                SKIP_DOCKER=true
                shift
                ;;
            --skip-backend)
                SKIP_BACKEND=true
                shift
                ;;
            --skip-frontend)
                SKIP_FRONTEND=true
                shift
                ;;
            --test-only)
                TEST_ONLY=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_warning "Unknown option: $1"
                shift
                ;;
        esac
    done

    print_status "Starting setup process..."
    print_status "Project directory: $PROJECT_DIR"
    print_status "Docker Compose directory: $DOCKER_COMPOSE_DIR"

    if [ "$TEST_ONLY" = true ]; then
        print_status "Running tests only..."
        check_docker
        check_python
        check_nodejs
        if [ "$SKIP_DOCKER" = false ]; then
            test_docker_services
        fi
        print_success "All tests passed!"
        exit 0
    fi

    # Run setup steps
    check_docker
    check_python
    check_nodejs

    if [ "$SKIP_DOCKER" = false ]; then
        setup_docker_env
    fi

    if [ "$SKIP_BACKEND" = false ]; then
        setup_backend
    fi

    if [ "$SKIP_FRONTEND" = false ]; then
        setup_frontend
    fi

    create_directories

    if [ "$SKIP_DOCKER" = false ]; then
        test_docker_services
    fi

    show_summary
}

# Run main function
main "$@"
