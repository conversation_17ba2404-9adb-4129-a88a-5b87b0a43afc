@echo off
REM Run Facebook Automation Desktop Application locally with Docker services
REM Uses Docker Compose for PostgreSQL and Redis, runs backend and frontend locally

setlocal enabledelayedexpansion

echo 🚀 Starting Facebook Automation Desktop Application locally with Docker services...

REM Configuration
set BACKEND_PORT=8000
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..
set DOCKER_COMPOSE_DIR=%PROJECT_DIR%\auto-login

REM PIDs for cleanup (Windows doesn't have PIDs like Unix, we'll use tasklist)
set BACKEND_PROCESS=
set FRONTEND_PROCESS=

REM Colors (limited in Windows CMD)
set INFO_COLOR=[94m
set SUCCESS_COLOR=[92m
set WARNING_COLOR=[93m
set ERROR_COLOR=[91m
set NC=[0m

REM Function to print colored output (simulated)
:print_status
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Cleanup function
:cleanup
call :print_status "Shutting down services..."

REM Kill backend processes
taskkill /f /im python.exe 2>nul
taskkill /f /im uvicorn.exe 2>nul

REM Kill frontend processes
taskkill /f /im electron.exe 2>nul
taskkill /f /im node.exe 2>nul

REM Stop Docker services
call :print_status "Stopping Docker services..."
cd /d "%DOCKER_COMPOSE_DIR%"
docker-compose down 2>nul

call :print_success "All services stopped"
exit /b 0

REM Check prerequisites
:check_prerequisites
call :print_status "Checking prerequisites..."

REM Check Docker
docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not installed"
    call :print_status "Please install Docker: https://docs.docker.com/get-docker/"
    exit /b 1
)

REM Check Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose is not installed"
    call :print_status "Please install Docker Compose: https://docs.docker.com/compose/install/"
    exit /b 1
)

REM Check if setup has been run
if not exist "%PROJECT_DIR%\backend\venv" (
    call :print_error "Backend virtual environment not found"
    call :print_status "Please run setup first: .\scripts\setup-local.bat"
    exit /b 1
)

if not exist "%PROJECT_DIR%\frontend\node_modules" (
    call :print_error "Frontend dependencies not found"
    call :print_status "Please run setup first: .\scripts\setup-local.bat"
    exit /b 1
)

REM Check Docker Compose file
if not exist "%DOCKER_COMPOSE_DIR%\docker-compose.yml" (
    call :print_error "Docker Compose file not found at %DOCKER_COMPOSE_DIR%\docker-compose.yml"
    exit /b 1
)

call :print_success "Prerequisites check passed"
goto :eof

REM Start Docker services
:start_docker_services
call :print_status "Starting Docker services (PostgreSQL and Redis)..."

cd /d "%DOCKER_COMPOSE_DIR%"

REM Check if .env.product exists
if not exist ".env.product" (
    call :print_warning ".env.product not found, creating from template..."
    (
        echo # Database Configuration
        echo DB_HOST=localhost
        echo DB_PORT=5432
        echo DB_USERNAME=postgres
        echo DB_PASSWORD=postgres123
        echo DB_DATABASE=facebook_automation
        echo.
        echo # Redis Configuration
        echo REDIS_HOST=localhost
        echo REDIS_PORT=6379
        echo REDIS_PASSWORD=redis123
        echo.
        echo # Application Configuration
        echo NODE_ENV=development
        echo JWT_SECRET=your-jwt-secret-key-here
        echo API_PORT=3000
    ) > .env.product
    call :print_success "Created .env.product file"
)

REM Start only PostgreSQL and Redis services
call :print_status "Starting PostgreSQL and Redis containers..."
docker-compose up -d postgres redis

REM Wait for services to be ready
call :print_status "Waiting for database services to be ready..."
timeout /t 10 /nobreak >nul

REM Check if services are running
docker-compose ps postgres | findstr "Up" >nul
if errorlevel 1 (
    call :print_error "Failed to start PostgreSQL"
    exit /b 1
) else (
    call :print_success "PostgreSQL is running"
)

docker-compose ps redis | findstr "Up" >nul
if errorlevel 1 (
    call :print_error "Failed to start Redis"
    exit /b 1
) else (
    call :print_success "Redis is running"
)

call :print_success "Docker services started successfully"
goto :eof

REM Start backend server
:start_backend
call :print_status "Starting backend server..."

cd /d "%PROJECT_DIR%\backend"

REM Check if main.py exists
if not exist "main.py" (
    call :print_error "Backend main.py not found"
    exit /b 1
)

REM Set environment variables for local development
set DB_HOST=localhost
set DB_PORT=5432
set DB_USERNAME=postgres
set DB_PASSWORD=postgres123
set DB_DATABASE=facebook_automation
set REDIS_HOST=localhost
set REDIS_PORT=6379
set REDIS_PASSWORD=redis123
set NODE_ENV=development

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Run database migrations
call :print_status "Running database migrations..."
if exist "alembic.ini" (
    alembic upgrade head 2>nul || call :print_warning "Migration failed or not needed"
)

REM Start backend with uvicorn
call :print_status "Starting FastAPI server on port %BACKEND_PORT%..."
start "Backend Server" cmd /c "uvicorn main:app --reload --host 0.0.0.0 --port %BACKEND_PORT%"

REM Wait a moment for server to start
timeout /t 5 /nobreak >nul

call :print_success "Backend server started"
call :print_status "Backend API: http://localhost:%BACKEND_PORT%"
call :print_status "API Docs: http://localhost:%BACKEND_PORT%/docs"

goto :eof

REM Start frontend Electron app
:start_frontend
call :print_status "Starting frontend Electron app..."

cd /d "%PROJECT_DIR%\frontend"

REM Check if package.json exists
if not exist "package.json" (
    call :print_error "Frontend package.json not found"
    exit /b 1
)

REM Set environment variables
set REACT_APP_API_URL=http://localhost:%BACKEND_PORT%
set REACT_APP_WS_URL=ws://localhost:%BACKEND_PORT%
set NODE_ENV=development

REM Start Electron app
call :print_status "Starting Electron desktop application..."
start "Electron App" cmd /c "npm run start"

REM Wait a moment for app to start
timeout /t 5 /nobreak >nul

call :print_success "Electron app started"
call :print_status "Desktop application should open automatically"

goto :eof

REM Show help
:show_help
echo Usage: %0 [OPTIONS]
echo.
echo Start Facebook Automation Desktop Application locally with Docker services
echo.
echo This script:
echo 1. Starts PostgreSQL and Redis using Docker Compose
echo 2. Starts the FastAPI backend server locally
echo 3. Starts the Electron desktop application
echo.
echo OPTIONS:
echo   --backend-only      Start only backend server (with Docker services)
echo   --frontend-only     Start only frontend app (requires backend running)
echo   --help              Show this help message
echo.
echo EXAMPLES:
echo   %0                          # Start all services (recommended)
echo   %0 --backend-only           # Start only backend with Docker services
echo   %0 --frontend-only          # Start only Electron app
echo   %0 --help                   # Show this help
echo.
echo PREREQUISITES:
echo   - Docker and Docker Compose installed
echo   - Backend virtual environment set up (run .\scripts\setup-local.bat)
echo   - Frontend dependencies installed (run .\scripts\setup-local.bat)
echo.
goto :eof

REM Main function
:main
set BACKEND_ONLY=false
set FRONTEND_ONLY=false

REM Parse arguments
:parse_args
if "%~1"=="" goto :start_services
if "%~1"=="--backend-only" (
    set BACKEND_ONLY=true
    shift
    goto :parse_args
)
if "%~1"=="--frontend-only" (
    set FRONTEND_ONLY=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    call :show_help
    exit /b 0
)
shift
goto :parse_args

:start_services
REM Validate conflicting options
if "%BACKEND_ONLY%"=="true" if "%FRONTEND_ONLY%"=="true" (
    call :print_error "Cannot use --backend-only and --frontend-only together"
    exit /b 1
)

call :print_status "Starting Facebook Automation Desktop Application..."
call :print_status "Project directory: %PROJECT_DIR%"
call :print_status "Docker Compose directory: %DOCKER_COMPOSE_DIR%"

REM Run checks
call :check_prerequisites
if errorlevel 1 exit /b 1

REM Start Docker services (unless frontend-only)
if not "%FRONTEND_ONLY%"=="true" (
    call :start_docker_services
    if errorlevel 1 exit /b 1
)

REM Start backend (unless frontend-only)
if not "%FRONTEND_ONLY%"=="true" (
    call :start_backend
    if errorlevel 1 exit /b 1
)

REM Start frontend (unless backend-only)
if not "%BACKEND_ONLY%"=="true" (
    call :start_frontend
    if errorlevel 1 exit /b 1
)

call :print_success "🎉 Application started successfully!"
echo.
call :print_status "Available services:"
if not "%FRONTEND_ONLY%"=="true" (
    echo   🐘 PostgreSQL: Running in Docker container
    echo   🔴 Redis: Running in Docker container
    echo   📡 Backend API: http://localhost:%BACKEND_PORT%
    echo   📚 API Documentation: http://localhost:%BACKEND_PORT%/docs
)
if not "%BACKEND_ONLY%"=="true" (
    echo   🖥️  Desktop App: Electron application window
)
echo.
call :print_status "Press Ctrl+C to stop services manually"
echo.

REM Keep the script running
pause
call :cleanup

goto :eof

REM Run main function
call :main %*
