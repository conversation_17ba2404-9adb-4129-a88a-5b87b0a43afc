#!/bin/bash

# Simple Desktop App Starter
# This script starts the desktop application without complex setup
# Prerequisites: Docker running with postgres and redis from auto-login/docker-compose.yml

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${BOLD}${BLUE}🚀 Facebook Automation Desktop - Simple Start${NC}"
echo -e "${BLUE}Project Root: $PROJECT_ROOT${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Check Docker services
check_services() {
    print_step "Checking Services"
    
    # Check Docker
    if ! docker ps >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_status "✅ Docker is running"
    
    # Start postgres and redis if not running
    cd "$PROJECT_ROOT/auto-login"
    if ! docker-compose ps | grep -q "postgres.*Up"; then
        print_status "Starting PostgreSQL..."
        docker-compose up -d postgres
        sleep 5
    fi
    
    if ! docker-compose ps | grep -q "redis.*Up"; then
        print_status "Starting Redis..."
        docker-compose up -d redis
        sleep 5
    fi
    
    print_status "✅ Database services are running"
    cd "$PROJECT_ROOT"
}

# Quick backend setup
setup_backend_quick() {
    print_step "Quick Backend Setup"
    
    cd "$PROJECT_ROOT/backend"
    
    # Create venv if not exists
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate venv
    source venv/bin/activate
    
    # Install minimal requirements
    print_status "Installing minimal Python dependencies..."
    pip install --upgrade pip

    # Install packages one by one to avoid build issues
    print_status "Installing core packages..."
    pip install fastapi uvicorn python-multipart python-dotenv aiofiles

    print_status "Installing database packages..."
    pip install sqlalchemy asyncpg || print_warning "asyncpg installation failed, continuing..."

    print_status "Installing Redis..."
    pip install redis || print_warning "redis installation failed, continuing..."
    
    # Create simple .env
    if [ ! -f ".env" ]; then
        print_status "Creating backend .env file..."
        cat > .env << 'EOF'
# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/auto_login
REDIS_URL=redis://:redis_password@localhost:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true

# Security
SECRET_KEY=simple-dev-key-not-for-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"]

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Profile Storage
PROFILES_DIR=../profiles
SHARED_PROFILES_DIR=../profiles/shared
TEMP_PROFILES_DIR=../profiles/temp

# Browser Configuration
CAMOUFOX_PATH=../camoufox
BROWSER_TIMEOUT=30000
HEADLESS=false
EOF
    fi
    
    # Create logs directory
    mkdir -p logs
    
    print_status "✅ Backend setup completed"
}

# Quick frontend setup
setup_frontend_quick() {
    print_step "Quick Frontend Setup"

    cd "$PROJECT_ROOT/frontend"

    # Install npm dependencies if not exists
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    fi

    # Build frontend for production
    print_status "Building frontend..."
    npm run webpack:build

    print_status "✅ Frontend setup completed"
}

# Start backend server
start_backend() {
    print_step "Starting Backend Server"
    
    cd "$PROJECT_ROOT/backend"
    source venv/bin/activate
    
    # Check if backend is already running
    if lsof -i :8000 >/dev/null 2>&1; then
        print_warning "Backend server is already running on port 8000"
        return 0
    fi
    
    print_status "Starting FastAPI backend server..."
    # Use simple main for testing
    nohup python main_simple.py > logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > backend.pid
    
    # Wait for backend to start
    print_status "Waiting for backend to start..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health >/dev/null 2>&1; then
            print_status "✅ Backend server started successfully"
            return 0
        fi
        sleep 1
    done
    
    print_error "❌ Backend server failed to start"
    if [ -f "logs/backend.log" ]; then
        print_error "Backend logs:"
        tail -10 logs/backend.log
    fi
    return 1
}

# Start desktop application
start_desktop() {
    print_step "Starting Desktop Application"
    
    cd "$PROJECT_ROOT/frontend"
    
    print_status "Launching Electron desktop app..."
    npm start
}

# Cleanup function
cleanup() {
    print_status "Cleaning up processes..."
    
    # Kill backend if we started it
    if [ -f "$PROJECT_ROOT/backend/backend.pid" ]; then
        BACKEND_PID=$(cat "$PROJECT_ROOT/backend/backend.pid")
        if kill -0 $BACKEND_PID 2>/dev/null; then
            print_status "Stopping backend server (PID: $BACKEND_PID)..."
            kill $BACKEND_PID
            rm -f "$PROJECT_ROOT/backend/backend.pid"
        fi
    fi
    
    print_status "Cleanup completed"
}

# Handle script interruption
trap cleanup EXIT INT TERM

# Display running information
show_info() {
    echo ""
    print_step "Application Running"
    echo -e "${GREEN}✅ PostgreSQL: localhost:5432 (from docker-compose)${NC}"
    echo -e "${GREEN}✅ Redis: localhost:6379 (from docker-compose)${NC}"
    echo -e "${GREEN}✅ Backend API: http://localhost:8000${NC}"
    echo -e "${GREEN}✅ Desktop App: Running in Electron${NC}"
    echo ""
    echo -e "${BLUE}🔗 Useful URLs:${NC}"
    echo -e "${BLUE}• API Documentation: http://localhost:8000/docs${NC}"
    echo -e "${BLUE}• Backend Health: http://localhost:8000/health${NC}"
    echo ""
    echo -e "${BLUE}📁 Logs:${NC}"
    echo -e "${BLUE}• Backend: backend/logs/backend.log${NC}"
    echo ""
    echo -e "${YELLOW}💡 Press Ctrl+C to stop all services${NC}"
}

# Main execution
main() {
    print_status "Starting simple desktop application..."
    
    # Check services
    check_services
    
    # Quick setup
    setup_backend_quick
    setup_frontend_quick
    
    # Create directories
    mkdir -p "$PROJECT_ROOT/profiles"
    mkdir -p "$PROJECT_ROOT/profiles/shared"
    mkdir -p "$PROJECT_ROOT/profiles/temp"
    mkdir -p "$PROJECT_ROOT/data"
    
    # Start services
    if start_backend; then
        # Show running information
        show_info
        
        # Start desktop application (this will block)
        start_desktop
    else
        print_error "Failed to start backend server. Check logs for details."
        exit 1
    fi
}

# Run main function
main "$@"
