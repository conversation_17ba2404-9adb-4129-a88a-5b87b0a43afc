#!/bin/bash

# Test Backend Setup
# Quick test to check if backend can start properly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}🧪 Testing Backend Setup${NC}"

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Test backend setup
test_backend() {
    cd "$PROJECT_ROOT/backend"
    
    # Check if venv exists
    if [ ! -d "venv" ]; then
        print_error "Virtual environment not found. Run setup first."
        return 1
    fi
    
    # Activate venv
    source venv/bin/activate
    
    # Check if main.py exists
    if [ ! -f "main.py" ]; then
        print_error "main.py not found in backend directory"
        return 1
    fi
    
    # Test import
    print_status "Testing Python imports..."
    if python -c "import sys; print('Python version:', sys.version)" 2>/dev/null; then
        print_status "✅ Python is working"
    else
        print_error "❌ Python test failed"
        return 1
    fi
    
    # Test FastAPI import
    if python -c "import fastapi; print('FastAPI version:', fastapi.__version__)" 2>/dev/null; then
        print_status "✅ FastAPI is available"
    else
        print_error "❌ FastAPI not found"
        return 1
    fi
    
    # Test asyncpg import
    if python -c "import asyncpg; print('asyncpg is available')" 2>/dev/null; then
        print_status "✅ asyncpg is available"
    else
        print_warning "⚠️ asyncpg not found, installing..."
        pip install asyncpg psycopg2-binary
    fi
    
    # Test basic app import
    print_status "Testing app import..."
    if python -c "
import sys
sys.path.append('.')
try:
    import main
    print('✅ Main module import successful')
except Exception as e:
    print('❌ Main module import failed:', str(e))
    sys.exit(1)
" 2>/dev/null; then
        print_status "✅ Main module can be imported"
    else
        print_error "❌ Main module import failed"
        print_status "Trying to start server anyway..."
    fi
    
    return 0
}

# Test database connection
test_database() {
    print_status "Testing database connection..."
    
    # Check if PostgreSQL is running
    if docker ps | grep -q postgres; then
        print_status "✅ PostgreSQL container is running"
        
        # Test connection
        if docker exec -i $(docker ps | grep postgres | awk '{print $1}') pg_isready -U postgres >/dev/null 2>&1; then
            print_status "✅ PostgreSQL is ready"
        else
            print_warning "⚠️ PostgreSQL connection test failed"
        fi
    else
        print_warning "⚠️ PostgreSQL container not found"
    fi
}

# Test Redis connection
test_redis() {
    print_status "Testing Redis connection..."
    
    # Check if Redis is running
    if docker ps | grep -q redis; then
        print_status "✅ Redis container is running"
        
        # Test connection
        if docker exec -i $(docker ps | grep redis | awk '{print $1}') redis-cli ping | grep -q PONG; then
            print_status "✅ Redis is ready"
        else
            print_warning "⚠️ Redis connection test failed"
        fi
    else
        print_warning "⚠️ Redis container not found"
    fi
}

# Main test
main() {
    print_status "Starting backend tests..."
    
    test_database
    test_redis
    
    if test_backend; then
        print_status "🎉 Backend tests passed!"
        echo ""
        echo -e "${GREEN}You can now run: ./scripts/run-desktop-app.sh${NC}"
    else
        print_error "❌ Backend tests failed"
        echo ""
        echo -e "${YELLOW}Try running the setup first or check the error messages above${NC}"
        exit 1
    fi
}

main "$@"
