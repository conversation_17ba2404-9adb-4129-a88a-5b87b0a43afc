#!/bin/bash

# Make all scripts executable
# Facebook Automation Desktop Application

echo "🔧 Making all scripts executable..."

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Make all .sh files executable
find "$SCRIPT_DIR" -name "*.sh" -type f -exec chmod +x {} \;

echo "✅ All scripts are now executable!"

# List executable scripts
echo ""
echo "📋 Available scripts:"
echo ""
echo "  🚀 Desktop Application Scripts (Khuyến nghị):"
echo "    - setup-local-desktop.sh  (Setup desktop app với Docker services)"
echo "    - run-local-desktop.sh    (Run desktop app với Docker PostgreSQL/Redis)"
echo ""
echo "  🔧 Traditional Development Scripts:"
echo "    - setup-local.sh          (Linux/macOS development setup)"
echo "    - setup-local.bat         (Windows development setup)"
echo "    - run-local.sh            (Run traditional development mode)"
echo "    - dev.sh                  (Quick development start)"
echo ""
echo "  🏗️ Build Scripts:"
echo "    - build-local.sh          (Development build)"
echo "    - build-local.bat         (Windows development build)"
echo "    - build-production.sh     (Production build)"
echo ""
echo "  🚀 Production Scripts:"
echo "    - setup-production.sh     (Production server setup)"
echo "    - deploy.sh               (Automated deployment)"
echo ""
echo "  🛠️ Utility Scripts:"
echo "    - make-executable.sh      (This script)"
echo ""
echo "💡 Quick Start (Desktop App):"
echo "  1. ./scripts/setup-local-desktop.sh"
echo "  2. ./scripts/run-local-desktop.sh"
echo ""
echo "📖 For detailed usage, see scripts/README.md"
