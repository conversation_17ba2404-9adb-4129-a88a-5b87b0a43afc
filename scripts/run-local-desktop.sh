#!/bin/bash

# Run Facebook Automation Desktop Application locally with Docker services
# Uses Docker Compose for PostgreSQL and Redis, runs backend and frontend locally

set -e  # Exit on any error

echo "🚀 Starting Facebook Automation Desktop Application locally with Docker services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
BACKEND_PORT=8000
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
DOCKER_COMPOSE_DIR="$PROJECT_DIR/auto-login"

# PIDs for cleanup
BACKEND_PID=""
FRONTEND_PID=""

# Cleanup function
cleanup() {
    print_status "Shutting down services..."
    
    if [ -n "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_status "Backend stopped"
    fi
    
    if [ -n "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_status "Frontend stopped"
    fi
    
    # Kill any remaining processes
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "electron" 2>/dev/null || true
    
    # Stop Docker services
    print_status "Stopping Docker services..."
    cd "$DOCKER_COMPOSE_DIR"
    docker-compose down 2>/dev/null || true
    
    print_success "All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        print_status "Please install Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        print_status "Please install Docker Compose: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # Check if setup has been run
    if [ ! -d "$PROJECT_DIR/backend/venv" ]; then
        print_error "Backend virtual environment not found"
        print_status "Please run setup first: ./scripts/setup-local.sh"
        exit 1
    fi
    
    if [ ! -d "$PROJECT_DIR/frontend/node_modules" ]; then
        print_error "Frontend dependencies not found"
        print_status "Please run setup first: ./scripts/setup-local.sh"
        exit 1
    fi
    
    # Check Docker Compose file
    if [ ! -f "$DOCKER_COMPOSE_DIR/docker-compose.yml" ]; then
        print_error "Docker Compose file not found at $DOCKER_COMPOSE_DIR/docker-compose.yml"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Start Docker services
start_docker_services() {
    print_status "Starting Docker services (PostgreSQL and Redis)..."
    
    cd "$DOCKER_COMPOSE_DIR"
    
    # Check if .env.product exists
    if [ ! -f ".env.product" ]; then
        print_warning ".env.product not found, creating from template..."
        cat > .env.product << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres123
DB_DATABASE=facebook_automation

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis123

# Application Configuration
NODE_ENV=development
JWT_SECRET=your-jwt-secret-key-here
API_PORT=3000
EOF
        print_success "Created .env.product file"
    fi
    
    # Start only PostgreSQL and Redis services
    print_status "Starting PostgreSQL and Redis containers..."
    docker-compose up -d postgres redis
    
    # Wait for services to be ready
    print_status "Waiting for database services to be ready..."
    sleep 10
    
    # Check if services are running
    if docker-compose ps postgres | grep -q "Up"; then
        print_success "PostgreSQL is running"
    else
        print_error "Failed to start PostgreSQL"
        exit 1
    fi
    
    if docker-compose ps redis | grep -q "Up"; then
        print_success "Redis is running"
    else
        print_error "Failed to start Redis"
        exit 1
    fi
    
    print_success "Docker services started successfully"
}

# Start backend server
start_backend() {
    print_status "Starting backend server..."
    
    cd "$PROJECT_DIR/backend"
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Check if main.py exists
    if [ ! -f "main.py" ]; then
        print_error "Backend main.py not found"
        exit 1
    fi
    
    # Set environment variables for local development
    export DB_HOST=localhost
    export DB_PORT=5432
    export DB_USERNAME=postgres
    export DB_PASSWORD=postgres123
    export DB_DATABASE=facebook_automation
    export REDIS_HOST=localhost
    export REDIS_PORT=6379
    export REDIS_PASSWORD=redis123
    export NODE_ENV=development
    
    # Run database migrations
    print_status "Running database migrations..."
    if [ -f "alembic.ini" ]; then
        alembic upgrade head 2>/dev/null || print_warning "Migration failed or not needed"
    fi
    
    # Start backend with uvicorn
    print_status "Starting FastAPI server on port $BACKEND_PORT..."
    uvicorn main:app --reload --host 0.0.0.0 --port $BACKEND_PORT &
    BACKEND_PID=$!
    
    # Wait a moment for server to start
    sleep 5
    
    # Check if backend started successfully
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_success "Backend server started (PID: $BACKEND_PID)"
        print_status "Backend API: http://localhost:$BACKEND_PORT"
        print_status "API Docs: http://localhost:$BACKEND_PORT/docs"
    else
        print_error "Failed to start backend server"
        exit 1
    fi
}

# Start frontend Electron app
start_frontend() {
    print_status "Starting frontend Electron app..."

    cd "$PROJECT_DIR/frontend"

    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "Frontend package.json not found"
        exit 1
    fi

    # Set environment variables
    export REACT_APP_API_URL="http://localhost:$BACKEND_PORT"
    export REACT_APP_WS_URL="ws://localhost:$BACKEND_PORT"
    export NODE_ENV=development

    # Start Electron app
    print_status "Starting Electron desktop application..."
    npm run start &
    FRONTEND_PID=$!

    # Wait a moment for app to start
    sleep 5

    # Check if frontend started successfully
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_success "Electron app started (PID: $FRONTEND_PID)"
        print_status "Desktop application should open automatically"
    else
        print_error "Failed to start Electron app"
        exit 1
    fi
}

# Show running services status
show_status() {
    print_status "Service Status:"
    echo ""

    # Check Docker services
    cd "$DOCKER_COMPOSE_DIR"
    if docker-compose ps postgres | grep -q "Up"; then
        print_success "✅ PostgreSQL: Running (Docker)"
    else
        print_error "❌ PostgreSQL: Not running"
    fi

    if docker-compose ps redis | grep -q "Up"; then
        print_success "✅ Redis: Running (Docker)"
    else
        print_error "❌ Redis: Not running"
    fi

    # Check local services
    if [ -n "$BACKEND_PID" ] && kill -0 $BACKEND_PID 2>/dev/null; then
        print_success "✅ Backend: Running (PID: $BACKEND_PID) - http://localhost:$BACKEND_PORT"
    else
        print_error "❌ Backend: Not running"
    fi

    if [ -n "$FRONTEND_PID" ] && kill -0 $FRONTEND_PID 2>/dev/null; then
        print_success "✅ Frontend: Running (PID: $FRONTEND_PID) - Electron Desktop App"
    else
        print_error "❌ Frontend: Not running"
    fi

    echo ""
}

# Monitor services
monitor_services() {
    print_status "Monitoring services... (Press Ctrl+C to stop)"
    echo ""

    while true; do
        # Check if backend is still running
        if [ -n "$BACKEND_PID" ] && ! kill -0 $BACKEND_PID 2>/dev/null; then
            print_error "Backend process died unexpectedly"
            cleanup
        fi

        # Check if frontend is still running
        if [ -n "$FRONTEND_PID" ] && ! kill -0 $FRONTEND_PID 2>/dev/null; then
            print_error "Frontend process died unexpectedly"
            cleanup
        fi

        # Check Docker services
        cd "$DOCKER_COMPOSE_DIR"
        if ! docker-compose ps postgres | grep -q "Up"; then
            print_error "PostgreSQL container stopped unexpectedly"
            cleanup
        fi

        if ! docker-compose ps redis | grep -q "Up"; then
            print_error "Redis container stopped unexpectedly"
            cleanup
        fi

        sleep 10
    done
}

# Show help
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Start Facebook Automation Desktop Application locally with Docker services

This script:
1. Starts PostgreSQL and Redis using Docker Compose
2. Starts the FastAPI backend server locally
3. Starts the Electron desktop application

OPTIONS:
  --backend-only      Start only backend server (with Docker services)
  --frontend-only     Start only frontend app (requires backend running)
  --port PORT         Backend port (default: $BACKEND_PORT)
  --status            Show current service status
  --stop              Stop all running services
  --help              Show this help message

EXAMPLES:
  $0                          # Start all services (recommended)
  $0 --backend-only           # Start only backend with Docker services
  $0 --frontend-only          # Start only Electron app
  $0 --port 8080              # Use custom backend port
  $0 --status                 # Show service status
  $0 --stop                   # Stop all services

PREREQUISITES:
  - Docker and Docker Compose installed
  - Backend virtual environment set up (run ./scripts/setup-local.sh)
  - Frontend dependencies installed (run ./scripts/setup-local.sh)

EOF
}

# Stop all services
stop_services() {
    print_status "Stopping all local services..."

    # Kill local processes
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "electron" 2>/dev/null || true

    # Stop Docker services
    cd "$DOCKER_COMPOSE_DIR"
    docker-compose down 2>/dev/null || true

    print_success "All services stopped"
}

# Main function
main() {
    # Parse arguments
    BACKEND_ONLY=false
    FRONTEND_ONLY=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-only)
                BACKEND_ONLY=true
                shift
                ;;
            --frontend-only)
                FRONTEND_ONLY=true
                shift
                ;;
            --port)
                BACKEND_PORT="$2"
                shift 2
                ;;
            --status)
                show_status
                exit 0
                ;;
            --stop)
                stop_services
                exit 0
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_warning "Unknown option: $1"
                shift
                ;;
        esac
    done

    # Validate conflicting options
    if [ "$BACKEND_ONLY" = true ] && [ "$FRONTEND_ONLY" = true ]; then
        print_error "Cannot use --backend-only and --frontend-only together"
        exit 1
    fi

    print_status "Starting Facebook Automation Desktop Application..."
    print_status "Project directory: $PROJECT_DIR"
    print_status "Docker Compose directory: $DOCKER_COMPOSE_DIR"

    # Run checks
    check_prerequisites

    # Start Docker services (unless frontend-only)
    if [ "$FRONTEND_ONLY" = false ]; then
        start_docker_services
    fi

    # Start backend (unless frontend-only)
    if [ "$FRONTEND_ONLY" = false ]; then
        start_backend
    fi

    # Start frontend (unless backend-only)
    if [ "$BACKEND_ONLY" = false ]; then
        start_frontend
    fi

    # Show status
    sleep 3
    show_status

    print_success "🎉 Application started successfully!"
    echo ""
    print_status "Available services:"
    if [ "$FRONTEND_ONLY" = false ]; then
        echo "  🐘 PostgreSQL: Running in Docker container"
        echo "  🔴 Redis: Running in Docker container"
        echo "  📡 Backend API: http://localhost:$BACKEND_PORT"
        echo "  📚 API Documentation: http://localhost:$BACKEND_PORT/docs"
    fi
    if [ "$BACKEND_ONLY" = false ]; then
        echo "  🖥️  Desktop App: Electron application window"
    fi
    echo ""
    print_status "Press Ctrl+C to stop all services"
    echo ""

    # Monitor services
    monitor_services
}

# Run main function
main "$@"
