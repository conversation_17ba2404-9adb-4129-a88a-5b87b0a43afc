@echo off
setlocal enabledelayedexpansion

REM Simple Desktop App Starter for Windows
REM This script starts the desktop application without complex setup
REM Prerequisites: Docker running with postgres and redis from auto-login/docker-compose.yml

echo.
echo ========================================
echo   Facebook Automation Desktop - Simple Start
echo ========================================

REM Get script directory
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

echo Project Root: %PROJECT_ROOT%

REM Function to print status
set "GREEN=[32m"
set "YELLOW=[33m"
set "RED=[31m"
set "BLUE=[34m"
set "CYAN=[36m"
set "BOLD=[1m"
set "NC=[0m"

echo.
echo === Checking Services ===

REM Check Docker
docker ps >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Docker is not running. Please start Docker first.
    pause
    exit /b 1
)
echo %GREEN%[INFO]%NC% Docker is running

REM Start postgres and redis if not running
cd /d "%PROJECT_ROOT%\auto-login"
docker-compose ps | findstr "postgres.*Up" >nul
if errorlevel 1 (
    echo %GREEN%[INFO]%NC% Starting PostgreSQL...
    docker-compose up -d postgres
    timeout /t 5 >nul
)

docker-compose ps | findstr "redis.*Up" >nul
if errorlevel 1 (
    echo %GREEN%[INFO]%NC% Starting Redis...
    docker-compose up -d redis
    timeout /t 5 >nul
)

echo %GREEN%[INFO]%NC% Database services are running
cd /d "%PROJECT_ROOT%"

echo.
echo === Quick Backend Setup ===

cd /d "%PROJECT_ROOT%\backend"

REM Create venv if not exists
if not exist "venv" (
    echo %GREEN%[INFO]%NC% Creating Python virtual environment...
    python -m venv venv
)

REM Activate venv
call venv\Scripts\activate.bat

REM Install minimal requirements
echo %GREEN%[INFO]%NC% Installing minimal Python dependencies...
pip install --upgrade pip

echo %GREEN%[INFO]%NC% Installing core packages...
pip install fastapi uvicorn python-multipart python-dotenv aiofiles

echo %GREEN%[INFO]%NC% Installing database packages...
pip install sqlalchemy asyncpg 2>nul || echo %YELLOW%[WARNING]%NC% asyncpg installation failed, continuing...

echo %GREEN%[INFO]%NC% Installing Redis...
pip install redis 2>nul || echo %YELLOW%[WARNING]%NC% redis installation failed, continuing...

REM Create simple .env
if not exist ".env" (
    echo %GREEN%[INFO]%NC% Creating backend .env file...
    (
        echo # Database Configuration
        echo DATABASE_URL=postgresql://postgres:postgres@localhost:5432/auto_login
        echo REDIS_URL=redis://:redis_password@localhost:6379/0
        echo.
        echo # API Configuration
        echo API_HOST=0.0.0.0
        echo API_PORT=8000
        echo DEBUG=true
        echo.
        echo # Security
        echo SECRET_KEY=simple-dev-key-not-for-production
        echo ALGORITHM=HS256
        echo ACCESS_TOKEN_EXPIRE_MINUTES=30
        echo.
        echo # CORS
        echo CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"]
        echo.
        echo # Logging
        echo LOG_LEVEL=INFO
        echo LOG_FILE=logs/app.log
        echo.
        echo # Profile Storage
        echo PROFILES_DIR=../profiles
        echo SHARED_PROFILES_DIR=../profiles/shared
        echo TEMP_PROFILES_DIR=../profiles/temp
        echo.
        echo # Browser Configuration
        echo CAMOUFOX_PATH=../camoufox
        echo BROWSER_TIMEOUT=30000
        echo HEADLESS=false
    ) > .env
)

REM Create logs directory
if not exist "logs" mkdir logs

echo %GREEN%[INFO]%NC% Backend setup completed

echo.
echo === Quick Frontend Setup ===

cd /d "%PROJECT_ROOT%\frontend"

REM Install npm dependencies if not exists
if not exist "node_modules" (
    echo %GREEN%[INFO]%NC% Installing frontend dependencies...
    npm install
)

REM Build frontend for production
echo %GREEN%[INFO]%NC% Building frontend...
npm run webpack:build

echo %GREEN%[INFO]%NC% Frontend setup completed

echo.
echo === Starting Backend Server ===

cd /d "%PROJECT_ROOT%\backend"
call venv\Scripts\activate.bat

REM Check if backend is already running
netstat -an | findstr ":8000" >nul
if not errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Backend server is already running on port 8000
    goto :start_desktop
)

echo %GREEN%[INFO]%NC% Starting FastAPI backend server...
start /b python main_simple.py > logs\backend.log 2>&1

REM Wait for backend to start
echo %GREEN%[INFO]%NC% Waiting for backend to start...
set /a count=0
:wait_loop
set /a count+=1
if %count% gtr 30 goto :backend_failed

timeout /t 1 >nul
curl -s http://localhost:8000/health >nul 2>&1
if errorlevel 1 goto :wait_loop

echo %GREEN%[INFO]%NC% Backend server started successfully
goto :start_desktop

:backend_failed
echo %RED%[ERROR]%NC% Backend server failed to start
if exist "logs\backend.log" (
    echo %RED%[ERROR]%NC% Backend logs:
    type logs\backend.log | more
)
pause
exit /b 1

:start_desktop
REM Create directories
if not exist "%PROJECT_ROOT%\profiles" mkdir "%PROJECT_ROOT%\profiles"
if not exist "%PROJECT_ROOT%\profiles\shared" mkdir "%PROJECT_ROOT%\profiles\shared"
if not exist "%PROJECT_ROOT%\profiles\temp" mkdir "%PROJECT_ROOT%\profiles\temp"
if not exist "%PROJECT_ROOT%\data" mkdir "%PROJECT_ROOT%\data"

echo.
echo === Application Running ===
echo %GREEN%✅ PostgreSQL: localhost:5432 (from docker-compose)%NC%
echo %GREEN%✅ Redis: localhost:6379 (from docker-compose)%NC%
echo %GREEN%✅ Backend API: http://localhost:8000%NC%
echo %GREEN%✅ Desktop App: Running in Electron%NC%
echo.
echo %BLUE%🔗 Useful URLs:%NC%
echo %BLUE%• API Documentation: http://localhost:8000/docs%NC%
echo %BLUE%• Backend Health: http://localhost:8000/health%NC%
echo.
echo %BLUE%📁 Logs:%NC%
echo %BLUE%• Backend: backend\logs\backend.log%NC%
echo.
echo %YELLOW%💡 Press Ctrl+C to stop all services%NC%

echo.
echo === Starting Desktop Application ===

cd /d "%PROJECT_ROOT%\frontend"

echo %GREEN%[INFO]%NC% Launching Electron desktop app...
npm start

REM Cleanup on exit
echo.
echo %GREEN%[INFO]%NC% Cleaning up processes...
taskkill /f /im python.exe 2>nul
echo %GREEN%[INFO]%NC% Cleanup completed

pause
