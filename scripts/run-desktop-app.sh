#!/bin/bash

# Run Desktop App for Local Environment
# This script runs the desktop application using existing PostgreSQL and Redis from docker-compose
# Prerequisites: Docker running with postgres and redis from auto-login/docker-compose.yml

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${BOLD}${BLUE}🚀 Facebook Automation Desktop - Local Run${NC}"
echo -e "${BLUE}Project Root: $PROJECT_ROOT${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Check if Docker is running
check_docker() {
    print_status "Checking Docker..."
    
    if ! docker ps >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_status "✅ Docker is running"
}

# Check and start existing PostgreSQL and Redis containers
check_database_services() {
    print_step "Checking Database Services"
    
    cd "$PROJECT_ROOT/auto-login"
    
    # Check if containers are running
    if docker-compose ps | grep -q "postgres.*Up" && docker-compose ps | grep -q "redis.*Up"; then
        print_status "✅ PostgreSQL and Redis are already running"
    else
        print_status "Starting PostgreSQL and Redis from docker-compose..."
        docker-compose up -d postgres redis
        
        # Wait for services to be ready
        print_status "Waiting for services to be ready..."
        sleep 10
        
        # Check PostgreSQL
        for i in {1..30}; do
            if docker-compose exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
                print_status "✅ PostgreSQL is ready"
                break
            fi
            sleep 1
        done
        
        # Check Redis
        if docker-compose exec -T redis redis-cli ping | grep -q PONG; then
            print_status "✅ Redis is ready"
        else
            print_warning "⚠️ Redis connection test failed"
        fi
    fi
    
    cd "$PROJECT_ROOT"
}

# Setup backend environment
setup_backend() {
    print_step "Setting up Backend Environment"
    
    cd "$PROJECT_ROOT/backend"
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    print_status "Activating virtual environment..."
    source venv/bin/activate
    
    # Upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install requirements
    print_status "Installing Python dependencies..."
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        # Install additional required packages for PostgreSQL async support
        print_status "Installing database drivers..."
        pip install asyncpg psycopg2-binary --no-deps || pip install psycopg2-binary || true
    else
        print_error "requirements.txt not found in backend directory"
        exit 1
    fi
    
    # Create .env file with correct database configuration
    if [ ! -f ".env" ]; then
        print_status "Creating backend .env file..."
        cat > .env << EOF
# Database Configuration (using existing docker-compose postgres)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/auto_login
SQLITE_DATABASE_URL=sqlite:///./facebook_automation.db

# Redis Configuration (using existing docker-compose redis)
REDIS_URL=redis://:redis123@localhost:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true

# Security
SECRET_KEY=your-secret-key-change-in-production-$(openssl rand -hex 16)
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"]

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Profile Storage
PROFILES_DIR=../profiles
SHARED_PROFILES_DIR=../profiles/shared
TEMP_PROFILES_DIR=../profiles/temp

# Browser Configuration
CAMOUFOX_PATH=../camoufox
BROWSER_TIMEOUT=30000
HEADLESS=false
EOF
    fi
    
    # Create logs directory
    mkdir -p logs
    
    # Initialize database if needed
    if [ -f "init_db.py" ]; then
        print_status "Initializing database..."
        if ! python init_db.py; then
            print_warning "Database initialization failed, but continuing..."
            print_warning "You may need to run database migrations manually later"
        fi
    fi
    
    print_status "✅ Backend environment ready"
}

# Setup frontend environment
setup_frontend() {
    print_step "Setting up Frontend Environment"
    
    cd "$PROJECT_ROOT/frontend"
    
    # Install npm dependencies if not exists
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    fi
    
    # Build frontend if dist doesn't exist
    if [ ! -d "dist" ]; then
        print_status "Building frontend..."
        npm run webpack:build
    fi
    
    print_status "✅ Frontend environment ready"
}

# Setup root dependencies
setup_root() {
    print_step "Setting up Root Dependencies"
    
    cd "$PROJECT_ROOT"
    
    # Install root dependencies if not exists
    if [ ! -d "node_modules" ]; then
        print_status "Installing root dependencies..."
        npm install
    fi
    
    print_status "✅ Root dependencies ready"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p "$PROJECT_ROOT/profiles"
    mkdir -p "$PROJECT_ROOT/profiles/shared"
    mkdir -p "$PROJECT_ROOT/profiles/temp"
    mkdir -p "$PROJECT_ROOT/data"
    mkdir -p "$PROJECT_ROOT/backend/logs"
    mkdir -p "$PROJECT_ROOT/frontend/logs"
    
    print_status "✅ Directories created"
}

# Start backend server
start_backend() {
    print_step "Starting Backend Server"
    
    cd "$PROJECT_ROOT/backend"
    source venv/bin/activate
    
    # Check if backend is already running
    if lsof -i :8000 >/dev/null 2>&1; then
        print_warning "Backend server is already running on port 8000"
        return 0
    fi
    
    print_status "Starting FastAPI backend server..."
    nohup python main.py > logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > backend.pid
    
    # Wait for backend to start
    print_status "Waiting for backend to start..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health >/dev/null 2>&1; then
            print_status "✅ Backend server started successfully"
            return 0
        fi
        sleep 1
    done
    
    print_error "❌ Backend server failed to start"
    return 1
}

# Start desktop application
start_desktop() {
    print_step "Starting Desktop Application"
    
    cd "$PROJECT_ROOT/frontend"
    
    print_status "Launching Electron desktop app..."
    npm start
}

# Cleanup function
cleanup() {
    print_status "Cleaning up processes..."
    
    # Kill backend if we started it
    if [ -f "$PROJECT_ROOT/backend/backend.pid" ]; then
        BACKEND_PID=$(cat "$PROJECT_ROOT/backend/backend.pid")
        if kill -0 $BACKEND_PID 2>/dev/null; then
            print_status "Stopping backend server (PID: $BACKEND_PID)..."
            kill $BACKEND_PID
            rm -f "$PROJECT_ROOT/backend/backend.pid"
        fi
    fi
    
    print_status "Cleanup completed"
}

# Handle script interruption
trap cleanup EXIT INT TERM

# Display running information
show_info() {
    echo ""
    print_step "Application Running"
    echo -e "${GREEN}✅ PostgreSQL: localhost:5432 (from docker-compose)${NC}"
    echo -e "${GREEN}✅ Redis: localhost:6379 (from docker-compose)${NC}"
    echo -e "${GREEN}✅ Backend API: http://localhost:8000${NC}"
    echo -e "${GREEN}✅ Desktop App: Running in Electron${NC}"
    echo ""
    echo -e "${BLUE}🔗 Useful URLs:${NC}"
    echo -e "${BLUE}• API Documentation: http://localhost:8000/docs${NC}"
    echo -e "${BLUE}• Backend Health: http://localhost:8000/health${NC}"
    echo ""
    echo -e "${BLUE}📁 Logs:${NC}"
    echo -e "${BLUE}• Backend: backend/logs/backend.log${NC}"
    echo -e "${BLUE}• Docker: docker-compose logs postgres redis${NC}"
    echo ""
    echo -e "${YELLOW}💡 Press Ctrl+C to stop all services${NC}"
}

# Main execution
main() {
    print_status "Starting desktop application setup and run..."
    
    # Check prerequisites
    check_docker
    
    # Setup database services
    check_database_services
    
    # Setup application components
    create_directories
    setup_backend
    setup_frontend
    setup_root
    
    # Start services
    if start_backend; then
        # Show running information
        show_info

        # Start desktop application (this will block)
        start_desktop
    else
        print_error "Failed to start backend server. Check logs for details."
        print_error "Backend logs:"
        if [ -f "$PROJECT_ROOT/backend/logs/backend.log" ]; then
            tail -20 "$PROJECT_ROOT/backend/logs/backend.log"
        fi
        exit 1
    fi
}

# Run main function
main "$@"
