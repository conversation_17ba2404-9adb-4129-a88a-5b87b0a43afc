@echo off
REM Run Desktop App for Local Environment (Windows)
REM This script runs the desktop application using existing PostgreSQL and Redis from docker-compose
REM Prerequisites: Docker running with postgres and redis from auto-login/docker-compose.yml

setlocal enabledelayedexpansion

REM Get script directory
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

echo === Facebook Automation Desktop - Local Run ===
echo Project Root: %PROJECT_ROOT%

REM Function to print status
:print_status
echo [INFO] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

:print_step
echo === %~1 ===
goto :eof

REM Check if Docker is running
:check_docker
call :print_status "Checking Docker..."

docker ps >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not running. Please start Docker first."
    exit /b 1
)

call :print_status "✓ Docker is running"
goto :eof

REM Check and start existing PostgreSQL and Redis containers
:check_database_services
call :print_step "Checking Database Services"

cd /d "%PROJECT_ROOT%\auto-login"

REM Check if containers are running
docker-compose ps | findstr "postgres.*Up" >nul && docker-compose ps | findstr "redis.*Up" >nul
if not errorlevel 1 (
    call :print_status "✓ PostgreSQL and Redis are already running"
) else (
    call :print_status "Starting PostgreSQL and Redis from docker-compose..."
    docker-compose up -d postgres redis
    
    REM Wait for services to be ready
    call :print_status "Waiting for services to be ready..."
    timeout /t 10 /nobreak >nul
    
    REM Check PostgreSQL
    set /a counter=0
    :wait_postgres
    set /a counter+=1
    if !counter! gtr 30 goto postgres_timeout
    
    docker-compose exec -T postgres pg_isready -U postgres >nul 2>&1
    if errorlevel 1 (
        timeout /t 1 /nobreak >nul
        goto wait_postgres
    )
    call :print_status "✓ PostgreSQL is ready"
    
    REM Check Redis
    docker-compose exec -T redis redis-cli ping | findstr PONG >nul
    if not errorlevel 1 (
        call :print_status "✓ Redis is ready"
    ) else (
        call :print_warning "⚠ Redis connection test failed"
    )
    goto postgres_ready
)

:postgres_timeout
call :print_warning "PostgreSQL took too long to start"

:postgres_ready
cd /d "%PROJECT_ROOT%"
goto :eof

REM Setup backend environment
:setup_backend
call :print_step "Setting up Backend Environment"

cd /d "%PROJECT_ROOT%\backend"

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    call :print_status "Creating Python virtual environment..."
    python -m venv venv
)

REM Activate virtual environment
call :print_status "Activating virtual environment..."
call venv\Scripts\activate.bat

REM Upgrade pip
call :print_status "Upgrading pip..."
pip install --upgrade pip

REM Install requirements
call :print_status "Installing Python dependencies..."
if exist "requirements.txt" (
    pip install -r requirements.txt
    REM Install additional required packages for PostgreSQL async support
    pip install asyncpg psycopg2-binary
) else (
    call :print_error "requirements.txt not found in backend directory"
    exit /b 1
)

REM Create .env file with correct database configuration
if not exist ".env" (
    call :print_status "Creating backend .env file..."
    (
        echo # Database Configuration ^(using existing docker-compose postgres^)
        echo DATABASE_URL=postgresql://postgres:postgres@localhost:5432/auto_login
        echo SQLITE_DATABASE_URL=sqlite:///./facebook_automation.db
        echo.
        echo # Redis Configuration ^(using existing docker-compose redis^)
        echo REDIS_URL=redis://:redis123@localhost:6379/0
        echo.
        echo # API Configuration
        echo API_HOST=0.0.0.0
        echo API_PORT=8000
        echo DEBUG=true
        echo.
        echo # Security
        echo SECRET_KEY=your-secret-key-change-in-production
        echo ALGORITHM=HS256
        echo ACCESS_TOKEN_EXPIRE_MINUTES=30
        echo.
        echo # CORS
        echo CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"]
        echo.
        echo # Logging
        echo LOG_LEVEL=INFO
        echo LOG_FILE=logs/app.log
        echo.
        echo # Profile Storage
        echo PROFILES_DIR=../profiles
        echo SHARED_PROFILES_DIR=../profiles/shared
        echo TEMP_PROFILES_DIR=../profiles/temp
        echo.
        echo # Browser Configuration
        echo CAMOUFOX_PATH=../camoufox
        echo BROWSER_TIMEOUT=30000
        echo HEADLESS=false
    ) > .env
)

REM Create logs directory
if not exist "logs" mkdir logs

REM Initialize database if needed
if exist "init_db.py" (
    call :print_status "Initializing database..."
    python init_db.py
    if errorlevel 1 (
        call :print_warning "Database initialization failed, but continuing..."
        call :print_warning "You may need to run database migrations manually later"
    )
)

call :print_status "✓ Backend environment ready"
goto :eof

REM Setup frontend environment
:setup_frontend
call :print_step "Setting up Frontend Environment"

cd /d "%PROJECT_ROOT%\frontend"

REM Install npm dependencies if not exists
if not exist "node_modules" (
    call :print_status "Installing frontend dependencies..."
    npm install
)

REM Build frontend if dist doesn't exist
if not exist "dist" (
    call :print_status "Building frontend..."
    npm run webpack:build
)

call :print_status "✓ Frontend environment ready"
goto :eof

REM Setup root dependencies
:setup_root
call :print_step "Setting up Root Dependencies"

cd /d "%PROJECT_ROOT%"

REM Install root dependencies if not exists
if not exist "node_modules" (
    call :print_status "Installing root dependencies..."
    npm install
)

call :print_status "✓ Root dependencies ready"
goto :eof

REM Create necessary directories
:create_directories
call :print_status "Creating necessary directories..."

if not exist "%PROJECT_ROOT%\profiles" mkdir "%PROJECT_ROOT%\profiles"
if not exist "%PROJECT_ROOT%\profiles\shared" mkdir "%PROJECT_ROOT%\profiles\shared"
if not exist "%PROJECT_ROOT%\profiles\temp" mkdir "%PROJECT_ROOT%\profiles\temp"
if not exist "%PROJECT_ROOT%\data" mkdir "%PROJECT_ROOT%\data"
if not exist "%PROJECT_ROOT%\backend\logs" mkdir "%PROJECT_ROOT%\backend\logs"
if not exist "%PROJECT_ROOT%\frontend\logs" mkdir "%PROJECT_ROOT%\frontend\logs"

call :print_status "✓ Directories created"
goto :eof

REM Start backend server
:start_backend
call :print_step "Starting Backend Server"

cd /d "%PROJECT_ROOT%\backend"
call venv\Scripts\activate.bat

REM Check if backend is already running
netstat -an | findstr ":8000" >nul
if not errorlevel 1 (
    call :print_warning "Backend server is already running on port 8000"
    goto backend_ready
)

call :print_status "Starting FastAPI backend server..."
start /b cmd /c "python main.py > logs\backend.log 2>&1"

REM Wait for backend to start
call :print_status "Waiting for backend to start..."
set /a counter=0
:wait_backend
set /a counter+=1
if !counter! gtr 30 (
    call :print_error "Backend server failed to start"
    exit /b 1
)

timeout /t 1 /nobreak >nul
netstat -an | findstr ":8000" >nul
if errorlevel 1 goto wait_backend

call :print_status "✓ Backend server started successfully"

:backend_ready
goto :eof

REM Start desktop application
:start_desktop
call :print_step "Starting Desktop Application"

cd /d "%PROJECT_ROOT%\frontend"

call :print_status "Launching Electron desktop app..."
npm start
goto :eof

REM Display running information
:show_info
echo.
call :print_step "Application Running"
echo ✓ PostgreSQL: localhost:5432 ^(from docker-compose^)
echo ✓ Redis: localhost:6379 ^(from docker-compose^)
echo ✓ Backend API: http://localhost:8000
echo ✓ Desktop App: Running in Electron
echo.
echo Useful URLs:
echo • API Documentation: http://localhost:8000/docs
echo • Backend Health: http://localhost:8000/health
echo.
echo Logs:
echo • Backend: backend\logs\backend.log
echo • Docker: docker-compose logs postgres redis
echo.
echo Press Ctrl+C to stop all services
goto :eof

REM Main execution
:main
call :print_status "Starting desktop application setup and run..."

REM Check prerequisites
call :check_docker

REM Setup database services
call :check_database_services

REM Setup application components
call :create_directories
call :setup_backend
call :setup_frontend
call :setup_root

REM Start services
call :start_backend

REM Show running information
call :show_info

REM Start desktop application (this will block)
call :start_desktop

goto :eof

REM Run main function
call :main %*
