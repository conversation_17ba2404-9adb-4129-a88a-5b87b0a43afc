# Profile Sharing Database Schema Design

## Overview
This document outlines the comprehensive database schema for implementing profile sharing functionality across the antidetect browser system. The schema supports complete browser data storage, synchronization, and sharing between users based on their package permissions.

## Database Architecture

### 1. Auto-Login Service Database (PostgreSQL)
Enhanced existing schema with profile sharing capabilities.

#### 1.1 Enhanced Account Entity
```typescript
// auto-login/src/modules/accounts/entities/account.entity.ts
@Entity('accounts')
export class Account {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  website_url: string;

  @Column({ nullable: true })
  username: string;

  @Column({ nullable: true })
  password: string;

  // NEW: Link to browser profile
  @Column({ nullable: true })
  browser_profile_id: number;

  // NEW: Profile synchronization status
  @Column({ default: 'not_synced' })
  sync_status: string; // not_synced, syncing, synced, error

  @Column({ nullable: true, type: 'text' })
  cookie_data: string;

  @Column({ type: 'timestamp', nullable: true })
  last_login: Date;

  // NEW: Last sync timestamp
  @Column({ type: 'timestamp', nullable: true })
  last_sync: Date;

  @ManyToMany(() => Package)
  @JoinTable({
    name: 'account_packages',
    joinColumn: { name: 'account_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'package_id', referencedColumnName: 'id' },
  })
  packages: Package[];

  // NEW: Profile sharing relationships
  @OneToMany(() => ProfileShare, (profileShare) => profileShare.account)
  profileShares: ProfileShare[];
}
```

#### 1.2 New Profile Share Entity
```typescript
// auto-login/src/modules/profiles/entities/profile-share.entity.ts
@Entity('profile_shares')
export class ProfileShare {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  account_id: number;

  @Column()
  shared_with_user_id: number;

  @Column()
  shared_by_admin_id: number;

  @Column({ default: 'active' })
  status: string; // active, suspended, revoked

  @Column({ type: 'jsonb', nullable: true })
  permissions: {
    can_modify_profile: boolean;
    can_view_history: boolean;
    can_export_data: boolean;
    access_level: 'read' | 'write' | 'full';
  };

  @Column({ type: 'timestamp', nullable: true })
  expires_at: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => Account, (account) => account.profileShares)
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'shared_with_user_id' })
  sharedWithUser: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'shared_by_admin_id' })
  sharedByAdmin: User;
}
```

#### 1.3 Profile Access Log Entity
```typescript
// auto-login/src/modules/profiles/entities/profile-access-log.entity.ts
@Entity('profile_access_logs')
export class ProfileAccessLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  user_id: number;

  @Column()
  account_id: number;

  @Column()
  action: string; // login, logout, sync, export, modify

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    ip_address?: string;
    user_agent?: string;
    session_duration?: number;
    data_modified?: string[];
  };

  @CreateDateColumn()
  created_at: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;
}
```

### 2. Backend Service Database (SQLite/PostgreSQL)
Enhanced with comprehensive browser data storage.

#### 2.1 Enhanced Profile Model
```python
# backend/app/models/profile.py
class Profile(Base):
    __tablename__ = "profiles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, index=True, nullable=False)
    profile_path = Column(String(500), nullable=False)
    
    # NEW: Link to account in auto-login service
    account_id = Column(Integer, nullable=True, index=True)
    
    # Proxy settings
    proxy_type = Column(String(50), default=ProxyType.NO_PROXY)
    proxy_host = Column(String(255), nullable=True)
    proxy_port = Column(Integer, nullable=True)
    proxy_username = Column(String(255), nullable=True)
    proxy_password = Column(String(255), nullable=True)
    
    # Browser fingerprint data
    fingerprint_data = Column(JSON, nullable=True)
    
    # Facebook login status
    facebook_logged_in = Column(Boolean, default=False)
    facebook_username = Column(String(255), nullable=True)
    cookies_data = Column(JSON, nullable=True)
    
    # NEW: Profile sharing and sync metadata
    is_shared = Column(Boolean, default=False)
    shared_by_admin_id = Column(Integer, nullable=True)
    sync_version = Column(Integer, default=1)
    last_sync_timestamp = Column(DateTime, nullable=True)
    
    # Status and metadata
    status = Column(String(50), default=ProfileStatus.CREATED)
    last_used = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # NEW: Relationships
    local_storage_data = relationship("ProfileLocalStorage", back_populates="profile")
    indexeddb_data = relationship("ProfileIndexedDB", back_populates="profile")
    browser_history = relationship("ProfileBrowserHistory", back_populates="profile")
    sync_logs = relationship("ProfileSyncLog", back_populates="profile")
```

#### 2.2 Profile Local Storage Model
```python
# backend/app/models/profile_storage.py
class ProfileLocalStorage(Base):
    __tablename__ = "profile_local_storage"

    id = Column(Integer, primary_key=True, index=True)
    profile_id = Column(Integer, ForeignKey("profiles.id"), nullable=False, index=True)
    domain = Column(String(255), nullable=False, index=True)
    key = Column(String(255), nullable=False)
    value = Column(Text, nullable=True)
    data_type = Column(String(50), default="string")  # string, number, boolean, object
    
    # Compression and encoding
    is_compressed = Column(Boolean, default=False)
    compression_type = Column(String(50), nullable=True)  # gzip, lz4
    encoding = Column(String(50), default="utf-8")
    
    # Sync metadata
    sync_version = Column(Integer, default=1)
    last_modified = Column(DateTime, server_default=func.now(), onupdate=func.now())
    checksum = Column(String(64), nullable=True)  # SHA-256 hash
    
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    profile = relationship("Profile", back_populates="local_storage_data")
    
    __table_args__ = (
        Index('idx_profile_domain_key', 'profile_id', 'domain', 'key', unique=True),
    )
```

#### 2.3 Profile IndexedDB Model
```python
class ProfileIndexedDB(Base):
    __tablename__ = "profile_indexeddb"

    id = Column(Integer, primary_key=True, index=True)
    profile_id = Column(Integer, ForeignKey("profiles.id"), nullable=False, index=True)
    domain = Column(String(255), nullable=False, index=True)
    database_name = Column(String(255), nullable=False)
    object_store = Column(String(255), nullable=False)
    key = Column(String(255), nullable=False)
    
    # Data storage
    value = Column(LargeBinary, nullable=True)  # Binary data for complex objects
    value_type = Column(String(50), nullable=False)  # json, binary, blob
    
    # Compression and metadata
    is_compressed = Column(Boolean, default=False)
    compression_type = Column(String(50), nullable=True)
    original_size = Column(Integer, nullable=True)
    compressed_size = Column(Integer, nullable=True)
    
    # Schema and versioning
    schema_version = Column(Integer, default=1)
    sync_version = Column(Integer, default=1)
    last_modified = Column(DateTime, server_default=func.now(), onupdate=func.now())
    checksum = Column(String(64), nullable=True)
    
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    profile = relationship("Profile", back_populates="indexeddb_data")
    
    __table_args__ = (
        Index('idx_profile_indexeddb', 'profile_id', 'domain', 'database_name', 'object_store', 'key', unique=True),
    )
```

#### 2.4 Profile Browser History Model
```python
class ProfileBrowserHistory(Base):
    __tablename__ = "profile_browser_history"

    id = Column(Integer, primary_key=True, index=True)
    profile_id = Column(Integer, ForeignKey("profiles.id"), nullable=False, index=True)
    
    url = Column(Text, nullable=False)
    title = Column(Text, nullable=True)
    visit_time = Column(DateTime, nullable=False)
    visit_count = Column(Integer, default=1)
    typed_count = Column(Integer, default=0)
    
    # Navigation metadata
    transition_type = Column(String(50), nullable=True)  # link, typed, bookmark, reload
    referrer_url = Column(Text, nullable=True)
    
    # Page metadata
    favicon_url = Column(Text, nullable=True)
    page_metadata = Column(JSON, nullable=True)  # Additional page info
    
    # Sync metadata
    sync_version = Column(Integer, default=1)
    last_modified = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    profile = relationship("Profile", back_populates="browser_history")
    
    __table_args__ = (
        Index('idx_profile_url_time', 'profile_id', 'url', 'visit_time'),
        Index('idx_profile_visit_time', 'profile_id', 'visit_time'),
    )
```

#### 2.5 Profile Sync Log Model
```python
class ProfileSyncLog(Base):
    __tablename__ = "profile_sync_logs"

    id = Column(Integer, primary_key=True, index=True)
    profile_id = Column(Integer, ForeignKey("profiles.id"), nullable=False, index=True)

    sync_type = Column(String(50), nullable=False)  # full, incremental, conflict_resolution
    sync_direction = Column(String(50), nullable=False)  # upload, download, bidirectional

    # Sync metadata
    started_at = Column(DateTime, nullable=False)
    completed_at = Column(DateTime, nullable=True)
    status = Column(String(50), nullable=False)  # pending, in_progress, completed, failed

    # Data statistics
    items_synced = Column(Integer, default=0)
    data_size_bytes = Column(BigInteger, default=0)
    conflicts_resolved = Column(Integer, default=0)

    # Error handling
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)

    # User context
    initiated_by_user_id = Column(Integer, nullable=True)
    user_ip_address = Column(String(45), nullable=True)

    created_at = Column(DateTime, server_default=func.now())

    profile = relationship("Profile", back_populates="sync_logs")
```

#### 2.6 Profile Configuration Model
```python
class ProfileConfiguration(Base):
    __tablename__ = "profile_configurations"

    id = Column(Integer, primary_key=True, index=True)
    profile_id = Column(Integer, ForeignKey("profiles.id"), nullable=False, index=True)

    # Browser preferences
    preferences = Column(JSON, nullable=True)  # Browser settings as JSON

    # Extensions data
    extensions = Column(JSON, nullable=True)  # Extension settings and data

    # Bookmarks
    bookmarks = Column(JSON, nullable=True)  # Bookmarks hierarchy

    # Security settings
    certificates = Column(JSON, nullable=True)  # SSL certificates
    security_settings = Column(JSON, nullable=True)  # Security preferences

    # Form data
    autofill_data = Column(JSON, nullable=True)  # Form autofill data

    # Sync metadata
    sync_version = Column(Integer, default=1)
    last_modified = Column(DateTime, server_default=func.now(), onupdate=func.now())
    checksum = Column(String(64), nullable=True)

    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    profile = relationship("Profile")
```

### 3. Data Relationships and Constraints

#### 3.1 Cross-Service Relationships
```sql
-- Link between auto-login accounts and backend profiles
-- This is maintained through account_id field in profiles table
-- and browser_profile_id field in accounts table

-- Profile sharing permissions are enforced through:
-- 1. ProfileShare entity in auto-login service
-- 2. Package-based access control
-- 3. Admin role verification
```

#### 3.2 Data Integrity Constraints
```sql
-- Ensure profile sharing consistency
ALTER TABLE profile_shares
ADD CONSTRAINT chk_share_expiry
CHECK (expires_at IS NULL OR expires_at > created_at);

-- Ensure sync version consistency
ALTER TABLE profile_local_storage
ADD CONSTRAINT chk_sync_version
CHECK (sync_version > 0);

-- Ensure data size limits
ALTER TABLE profile_indexeddb
ADD CONSTRAINT chk_data_size
CHECK (compressed_size IS NULL OR compressed_size <= original_size);
```

### 4. Storage Optimization Strategy

#### 4.1 Data Compression
- **Text Data**: GZIP compression for JSON and text content
- **Binary Data**: LZ4 compression for speed-optimized binary data
- **Large Objects**: Chunked compression for objects > 1MB
- **Deduplication**: SHA-256 checksums to identify duplicate data

#### 4.2 Partitioning Strategy
```sql
-- Partition profile data by profile_id for better performance
CREATE TABLE profile_local_storage_p1 PARTITION OF profile_local_storage
FOR VALUES FROM (1) TO (1000);

CREATE TABLE profile_local_storage_p2 PARTITION OF profile_local_storage
FOR VALUES FROM (1000) TO (2000);

-- Time-based partitioning for history data
CREATE TABLE profile_browser_history_2024 PARTITION OF profile_browser_history
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

#### 4.3 Indexing Strategy
```sql
-- Composite indexes for common queries
CREATE INDEX idx_profile_domain_sync ON profile_local_storage(profile_id, domain, sync_version);
CREATE INDEX idx_profile_history_time ON profile_browser_history(profile_id, visit_time DESC);
CREATE INDEX idx_sync_logs_status ON profile_sync_logs(profile_id, status, started_at);

-- Partial indexes for active data
CREATE INDEX idx_active_shares ON profile_shares(account_id, shared_with_user_id)
WHERE status = 'active';
```

### 5. Security and Privacy Considerations

#### 5.1 Data Encryption
- **At-Rest**: AES-256 encryption for sensitive profile data
- **In-Transit**: TLS 1.3 for all data transmission
- **Key Management**: Separate encryption keys per profile
- **Field-Level**: Encrypt passwords, cookies, and personal data

#### 5.2 Access Control
- **Role-Based**: Admin-only profile sharing management
- **Package-Based**: User access tied to purchased packages
- **Time-Based**: Expirable profile sharing permissions
- **Audit Trail**: Complete logging of all profile access

#### 5.3 Data Isolation
- **User Isolation**: Strict separation of user data
- **Profile Isolation**: Prevent cross-profile data leakage
- **Session Isolation**: Separate browser sessions per user
- **Network Isolation**: Proxy-based network separation

This schema provides a comprehensive foundation for implementing the profile sharing system with full browser data synchronization, proper access control, and scalable storage architecture.
```
