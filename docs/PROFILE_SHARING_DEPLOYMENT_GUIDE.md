# Profile Sharing System - Deployment Guide

## Overview

This guide covers the deployment and testing of the comprehensive Profile Sharing System that enables secure sharing and synchronization of browser profiles across users.

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │  Auto-Login     │
│   (React)       │◄──►│   (FastAPI)     │◄──►│  Service        │
│                 │    │                 │    │  (NestJS)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Browser       │    │   PostgreSQL    │    │   PostgreSQL    │
│   Profiles      │    │   Database      │    │   Database      │
│   (Camoufox)    │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   Redis Cache   │    │   File Storage  │
│                 │    │   (Profiles)    │
└─────────────────┘    └─────────────────┘
```

## Prerequisites

### System Requirements
- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- Redis 6+
- Docker & Docker Compose (optional)

### Required Python Packages
```bash
pip install -r requirements.txt
```

Key packages:
- FastAPI
- SQLAlchemy (async)
- Camoufox
- Redis
- Cryptography
- LZ4
- Pytest

### Required Node.js Packages
```bash
cd frontend && npm install
```

Key packages:
- React 18
- Ant Design
- Axios
- Socket.io-client

## Installation & Setup

### 1. Database Setup

#### PostgreSQL Setup
```sql
-- Create databases
CREATE DATABASE antidetect_backend;
CREATE DATABASE antidetect_autologin;

-- Create users
CREATE USER backend_user WITH PASSWORD 'your_password';
CREATE USER autologin_user WITH PASSWORD 'your_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE antidetect_backend TO backend_user;
GRANT ALL PRIVILEGES ON DATABASE antidetect_autologin TO autologin_user;
```

#### Redis Setup
```bash
# Install Redis
sudo apt-get install redis-server

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 2. Environment Configuration

#### Backend (.env)
```env
# Database
DATABASE_URL=postgresql+asyncpg://backend_user:password@localhost/antidetect_backend

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key-here
PROFILE_ENCRYPTION_KEY=your-encryption-key-here

# Auto-login service
AUTO_LOGIN_SERVICE_URL=http://localhost:3000

# Performance
ENABLE_CACHING=true
ENABLE_COMPRESSION=true
ENABLE_PERFORMANCE_MONITORING=true
```

#### Auto-login Service (.env)
```env
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=autologin_user
DATABASE_PASSWORD=password
DATABASE_NAME=antidetect_autologin

# JWT
JWT_SECRET=your-jwt-secret-here
JWT_EXPIRES_IN=24h

# API
PORT=3000
```

#### Frontend (.env)
```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
```

### 3. Database Migration

#### Backend Database
```bash
cd backend
python -m alembic upgrade head
```

#### Auto-login Database
```bash
cd auto-login
npm run migration:run
```

### 4. Service Startup

#### Start Backend
```bash
cd backend
python main.py
```

#### Start Auto-login Service
```bash
cd auto-login
npm run start:dev
```

#### Start Frontend
```bash
cd frontend
npm start
```

## Testing

### Test Environment Setup

1. **Install Test Dependencies**
```bash
cd backend
pip install pytest pytest-asyncio pytest-cov pytest-html pytest-timeout
```

2. **Setup Test Database**
```bash
# Create test database
createdb antidetect_test

# Set test environment
export TESTING=true
export DATABASE_URL=postgresql+asyncpg://user:pass@localhost/antidetect_test
```

### Running Tests

#### All Tests
```bash
cd backend
python run_tests.py --type all --verbose
```

#### Unit Tests Only
```bash
python run_tests.py --type unit
```

#### Integration Tests
```bash
python run_tests.py --type integration
```

#### Security Tests
```bash
python run_tests.py --type security
```

#### Performance Tests
```bash
python run_tests.py --type performance
```

#### With Coverage
```bash
python run_tests.py --type coverage
```

#### Specific Test
```bash
python run_tests.py --specific tests/test_profile_sharing.py::TestProfileSyncService::test_capture_profile_data
```

### Test Reports

Test reports are generated in `backend/test_reports/`:
- `all_tests.html` - Comprehensive test report
- `coverage_html/` - Coverage report
- `test_report.json` - Machine-readable summary

## Production Deployment

### Docker Deployment

1. **Build Images**
```bash
# Backend
cd backend
docker build -t profile-sharing-backend .

# Frontend
cd frontend
docker build -t profile-sharing-frontend .

# Auto-login
cd auto-login
docker build -t profile-sharing-autologin .
```

2. **Docker Compose**
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: antidetect_backend
      POSTGRES_USER: backend_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  backend:
    image: profile-sharing-backend
    depends_on:
      - postgres
      - redis
    environment:
      DATABASE_URL: postgresql+asyncpg://backend_user:secure_password@postgres/antidetect_backend
      REDIS_URL: redis://redis:6379
    ports:
      - "8000:8000"

  autologin:
    image: profile-sharing-autologin
    depends_on:
      - postgres
    environment:
      DATABASE_HOST: postgres
      DATABASE_USERNAME: autologin_user
      DATABASE_PASSWORD: secure_password
    ports:
      - "3000:3000"

  frontend:
    image: profile-sharing-frontend
    ports:
      - "80:80"

volumes:
  postgres_data:
  redis_data:
```

3. **Deploy**
```bash
docker-compose up -d
```

### Manual Production Setup

1. **System Dependencies**
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3.9 python3-pip nodejs npm postgresql redis-server nginx

# CentOS/RHEL
sudo yum install python39 python3-pip nodejs npm postgresql redis nginx
```

2. **Application Setup**
```bash
# Clone repository
git clone <repository-url>
cd antidetect-browser

# Backend setup
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Frontend setup
cd ../frontend
npm install
npm run build

# Auto-login setup
cd ../auto-login
npm install
npm run build
```

3. **Process Management (systemd)**

Create service files:

**Backend Service** (`/etc/systemd/system/profile-sharing-backend.service`)
```ini
[Unit]
Description=Profile Sharing Backend
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/backend
Environment=PATH=/path/to/backend/venv/bin
ExecStart=/path/to/backend/venv/bin/python main.py
Restart=always

[Install]
WantedBy=multi-user.target
```

**Auto-login Service** (`/etc/systemd/system/profile-sharing-autologin.service`)
```ini
[Unit]
Description=Profile Sharing Auto-login
After=network.target postgresql.service

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/auto-login
ExecStart=/usr/bin/node dist/main.js
Restart=always

[Install]
WantedBy=multi-user.target
```

4. **Nginx Configuration**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Frontend
    location / {
        root /path/to/frontend/build;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Auto-login API
    location /auth/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # WebSocket
    location /ws/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

5. **SSL Configuration (Let's Encrypt)**
```bash
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## Monitoring & Maintenance

### Health Checks

1. **Backend Health**
```bash
curl http://localhost:8000/health
```

2. **Auto-login Health**
```bash
curl http://localhost:3000/health
```

3. **Database Health**
```bash
# PostgreSQL
pg_isready -h localhost -p 5432

# Redis
redis-cli ping
```

### Performance Monitoring

Access performance dashboard at:
- Backend: `http://localhost:8000/api/profile-performance/dashboard`
- Security: `http://localhost:8000/api/profile-security/security/status`

### Log Management

Logs are stored in:
- Backend: `backend/logs/`
- Auto-login: `auto-login/logs/`
- Nginx: `/var/log/nginx/`

### Backup Strategy

1. **Database Backup**
```bash
# PostgreSQL
pg_dump antidetect_backend > backup_$(date +%Y%m%d).sql

# Redis
redis-cli BGSAVE
```

2. **Profile Data Backup**
```bash
# Backup profile directories
tar -czf profiles_backup_$(date +%Y%m%d).tar.gz /path/to/profiles/
```

### Security Considerations

1. **Firewall Configuration**
```bash
# Allow only necessary ports
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

2. **Database Security**
- Use strong passwords
- Enable SSL connections
- Restrict network access
- Regular security updates

3. **Application Security**
- Keep dependencies updated
- Monitor security logs
- Regular security audits
- Enable audit logging

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

2. **Redis Connection Issues**
```bash
# Check Redis status
sudo systemctl status redis

# Test connection
redis-cli ping
```

3. **Profile Sync Issues**
```bash
# Check profile permissions
ls -la /path/to/profiles/

# Check disk space
df -h
```

4. **Performance Issues**
```bash
# Check system resources
htop
iostat -x 1

# Check application logs
tail -f backend/logs/app.log
```

### Support

For additional support:
1. Check application logs
2. Review test reports
3. Monitor performance dashboard
4. Contact system administrator

## Version History

- v1.0.0 - Initial release with basic profile sharing
- v1.1.0 - Added security features and encryption
- v1.2.0 - Performance optimization and caching
- v1.3.0 - Comprehensive testing framework
