import {
  Controller,
  Get,
  Query,
  ParseFloatPipe,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { CurrencyService } from './currency.service';

@Controller('currency')
export class CurrencyController {
  constructor(private readonly currencyService: CurrencyService) {}

  /**
   * Get current USDT exchange rate
   */
  @Get('usdt-rate')
  async getUSDTRate() {
    try {
      const rate = await this.currencyService.getUSDTRate();
      return {
        success: true,
        data: {
          rate,
          description: '1 USD = ' + rate.toFixed(4) + ' USDT',
        },
      };
    } catch (error) {
      throw new HttpException(
        'Failed to get USDT exchange rate',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Convert USD amount to USDT
   */
  @Get('convert-to-usdt')
  async convertToUSDT(@Query('amount', ParseFloatPipe) amount: number) {
    try {
      if (amount <= 0) {
        throw new HttpException(
          'Amount must be greater than 0',
          HttpStatus.BAD_REQUEST,
        );
      }

      const conversion = await this.currencyService.convertUSDToUSDT(amount);
      return {
        success: true,
        data: conversion,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to convert currency',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get multi-currency conversion for USD amount
   */
  @Get('multi-convert')
  async getMultiCurrencyConversion(
    @Query('amount', ParseFloatPipe) amount: number,
  ) {
    try {
      if (amount <= 0) {
        throw new HttpException(
          'Amount must be greater than 0',
          HttpStatus.BAD_REQUEST,
        );
      }

      const conversions =
        await this.currencyService.getMultiCurrencyConversion(amount);
      return {
        success: true,
        data: conversions,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to get currency conversions',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get cache information (for debugging)
   */
  @Get('cache-info')
  getCacheInfo() {
    const cacheInfo = this.currencyService.getCacheInfo();
    return {
      success: true,
      data: cacheInfo,
    };
  }

  /**
   * Clear exchange rate cache
   */
  @Get('clear-cache')
  clearCache() {
    this.currencyService.clearCache();
    return {
      success: true,
      message: 'Exchange rate cache cleared',
    };
  }
}
