import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

export interface ExchangeRate {
  currency: string;
  rate: number;
  lastUpdated: Date;
}

export interface CurrencyConversionResult {
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  convertedCurrency: string;
  exchangeRate: number;
  formattedAmount: string;
}

@Injectable()
export class CurrencyService {
  private readonly logger = new Logger(CurrencyService.name);
  private exchangeRates: Map<string, ExchangeRate> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

  constructor(private configService: ConfigService) {}

  /**
   * Get current USDT exchange rate from USD
   * Uses multiple APIs with fallback for better reliability
   */
  async getUSDTRate(): Promise<number> {
    const cacheKey = 'USDT';
    const cached = this.exchangeRates.get(cacheKey);

    // Return cached rate if still valid
    if (
      cached &&
      Date.now() - cached.lastUpdated.getTime() < this.CACHE_DURATION
    ) {
      return cached.rate;
    }

    // Try multiple APIs for better reliability
    const apis = [
      {
        name: 'CoinGecko',
        url: 'https://api.coingecko.com/api/v3/simple/price?ids=tether&vs_currencies=usd',
        parser: (data: any) => data?.tether?.usd || null,
      },
      {
        name: 'CoinCap',
        url: 'https://api.coincap.io/v2/assets/tether',
        parser: (data: any) => parseFloat(data?.data?.priceUsd) || null,
      },
    ];

    for (const api of apis) {
      try {
        this.logger.log(`Fetching USDT rate from ${api.name}...`);

        const response = await axios.get(api.url, {
          timeout: 5000,
          headers: {
            Accept: 'application/json',
            'User-Agent': 'Kitsify-Currency-Service/1.0',
          },
        });

        const usdtPrice = api.parser(response.data);

        if (usdtPrice && usdtPrice > 0) {
          const rate = 1 / usdtPrice; // Convert to USD to USDT rate

          // Cache the rate
          this.exchangeRates.set(cacheKey, {
            currency: 'USDT',
            rate: rate,
            lastUpdated: new Date(),
          });

          this.logger.log(
            `Updated USDT exchange rate from ${api.name}: 1 USD = ${rate.toFixed(4)} USDT`,
          );
          return rate;
        }
      } catch (error) {
        this.logger.warn(
          `Failed to fetch USDT rate from ${api.name}:`,
          error.message,
        );
        continue; // Try next API
      }
    }

    // All APIs failed, use cached rate or fallback
    if (cached) {
      this.logger.warn('Using cached USDT rate due to all API failures');
      return cached.rate;
    }

    this.logger.warn('Using default USDT rate (1:1) due to all API failures');
    return 1.0; // Fallback to 1:1 ratio
  }

  /**
   * Convert USD amount to USDT
   */
  async convertUSDToUSDT(usdAmount: number): Promise<CurrencyConversionResult> {
    const exchangeRate = await this.getUSDTRate();
    const convertedAmount = usdAmount * exchangeRate;

    return {
      originalAmount: usdAmount,
      originalCurrency: 'USD',
      convertedAmount: convertedAmount,
      convertedCurrency: 'USDT',
      exchangeRate: exchangeRate,
      formattedAmount: this.formatUSDT(convertedAmount),
    };
  }

  /**
   * Format USDT amount for display
   */
  formatUSDT(amount: number): string {
    return `${amount.toFixed(2)} USDT`;
  }

  /**
   * Format USD amount for display
   */
  formatUSD(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }

  /**
   * Get multiple currency conversions for a USD amount
   */
  async getMultiCurrencyConversion(usdAmount: number): Promise<{
    usd: { amount: number; formatted: string };
    usdt: CurrencyConversionResult;
  }> {
    const usdtConversion = await this.convertUSDToUSDT(usdAmount);

    return {
      usd: {
        amount: usdAmount,
        formatted: this.formatUSD(usdAmount),
      },
      usdt: usdtConversion,
    };
  }

  /**
   * Get comprehensive pricing information with discounts and multiple currencies
   */
  async getPricingWithDiscountAndCurrencies(
    originalPrice: number,
    discountPercent: number = 0,
  ): Promise<{
    original: { usd: number; usdt: CurrencyConversionResult };
    discounted: { usd: number; usdt: CurrencyConversionResult };
    discount: { amount: number; percent: number };
  }> {
    const discountedPrice = originalPrice * (1 - discountPercent / 100);
    const discountAmount = originalPrice - discountedPrice;

    const [originalUSDT, discountedUSDT] = await Promise.all([
      this.convertUSDToUSDT(originalPrice),
      this.convertUSDToUSDT(discountedPrice),
    ]);

    return {
      original: {
        usd: originalPrice,
        usdt: originalUSDT,
      },
      discounted: {
        usd: discountedPrice,
        usdt: discountedUSDT,
      },
      discount: {
        amount: discountAmount,
        percent: discountPercent,
      },
    };
  }

  /**
   * Format price with currency symbol
   */
  formatPrice(amount: number, currency: string = 'USD'): string {
    if (currency.toUpperCase() === 'USDT') {
      return this.formatUSDT(amount);
    }
    return this.formatUSD(amount);
  }

  /**
   * Clear exchange rate cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.exchangeRates.clear();
    this.logger.log('Exchange rate cache cleared');
  }

  /**
   * Get cached exchange rates info
   */
  getCacheInfo(): Array<{ currency: string; rate: number; age: number }> {
    const now = Date.now();
    return Array.from(this.exchangeRates.entries()).map(([currency, data]) => ({
      currency,
      rate: data.rate,
      age: now - data.lastUpdated.getTime(),
    }));
  }
}
