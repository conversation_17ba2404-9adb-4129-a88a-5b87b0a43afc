"""Add profile sharing tables

Revision ID: 001_profile_sharing
Revises: 
Create Date: 2024-07-24 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_profile_sharing'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Add new columns to existing profiles table
    op.add_column('profiles', sa.Column('account_id', sa.Integer(), nullable=True))
    op.add_column('profiles', sa.Column('is_shared', sa.<PERSON>(), default=False))
    op.add_column('profiles', sa.Column('shared_by_admin_id', sa.Integer(), nullable=True))
    op.add_column('profiles', sa.Column('sync_version', sa.Integer(), default=1))
    op.add_column('profiles', sa.Column('last_sync_timestamp', sa.DateTime(), nullable=True))
    
    # Create index on account_id
    op.create_index('idx_profiles_account_id', 'profiles', ['account_id'])
    
    # Create profile_local_storage table
    op.create_table('profile_local_storage',
        sa.Column('id', sa.Integer(), primary_key=True, index=True),
        sa.Column('profile_id', sa.Integer(), sa.ForeignKey('profiles.id'), nullable=False, index=True),
        sa.Column('domain', sa.String(255), nullable=False, index=True),
        sa.Column('key', sa.String(255), nullable=False),
        sa.Column('value', sa.Text(), nullable=True),
        sa.Column('data_type', sa.String(50), default='string'),
        sa.Column('is_compressed', sa.Boolean(), default=False),
        sa.Column('compression_type', sa.String(50), nullable=True),
        sa.Column('encoding', sa.String(50), default='utf-8'),
        sa.Column('original_size', sa.Integer(), nullable=True),
        sa.Column('compressed_size', sa.Integer(), nullable=True),
        sa.Column('sync_version', sa.Integer(), default=1),
        sa.Column('last_modified', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('checksum', sa.String(64), nullable=True),
        sa.Column('sync_status', sa.String(50), default='pending'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now()),
    )
    
    # Create indexes for profile_local_storage
    op.create_index('idx_profile_domain_key', 'profile_local_storage', ['profile_id', 'domain', 'key'], unique=True)
    op.create_index('idx_profile_sync_status', 'profile_local_storage', ['profile_id', 'sync_status'])
    op.create_index('idx_domain_modified', 'profile_local_storage', ['domain', 'last_modified'])
    
    # Create profile_indexeddb table
    op.create_table('profile_indexeddb',
        sa.Column('id', sa.Integer(), primary_key=True, index=True),
        sa.Column('profile_id', sa.Integer(), sa.ForeignKey('profiles.id'), nullable=False, index=True),
        sa.Column('domain', sa.String(255), nullable=False, index=True),
        sa.Column('database_name', sa.String(255), nullable=False),
        sa.Column('object_store', sa.String(255), nullable=False),
        sa.Column('key', sa.String(255), nullable=False),
        sa.Column('value', sa.LargeBinary(), nullable=True),
        sa.Column('value_type', sa.String(50), nullable=False, default='json'),
        sa.Column('is_compressed', sa.Boolean(), default=False),
        sa.Column('compression_type', sa.String(50), nullable=True),
        sa.Column('original_size', sa.Integer(), nullable=True),
        sa.Column('compressed_size', sa.Integer(), nullable=True),
        sa.Column('schema_version', sa.Integer(), default=1),
        sa.Column('sync_version', sa.Integer(), default=1),
        sa.Column('last_modified', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('checksum', sa.String(64), nullable=True),
        sa.Column('sync_status', sa.String(50), default='pending'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now()),
    )
    
    # Create indexes for profile_indexeddb
    op.create_index('idx_profile_indexeddb', 'profile_indexeddb', 
                   ['profile_id', 'domain', 'database_name', 'object_store', 'key'], unique=True)
    op.create_index('idx_profile_indexeddb_sync', 'profile_indexeddb', ['profile_id', 'sync_status'])
    op.create_index('idx_indexeddb_size', 'profile_indexeddb', ['original_size', 'compressed_size'])
    
    # Create profile_browser_history table
    op.create_table('profile_browser_history',
        sa.Column('id', sa.Integer(), primary_key=True, index=True),
        sa.Column('profile_id', sa.Integer(), sa.ForeignKey('profiles.id'), nullable=False, index=True),
        sa.Column('url', sa.Text(), nullable=False),
        sa.Column('title', sa.Text(), nullable=True),
        sa.Column('visit_time', sa.DateTime(), nullable=False),
        sa.Column('visit_count', sa.Integer(), default=1),
        sa.Column('typed_count', sa.Integer(), default=0),
        sa.Column('transition_type', sa.String(50), nullable=True),
        sa.Column('referrer_url', sa.Text(), nullable=True),
        sa.Column('favicon_url', sa.Text(), nullable=True),
        sa.Column('page_metadata', sa.JSON(), nullable=True),
        sa.Column('sync_version', sa.Integer(), default=1),
        sa.Column('last_modified', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('sync_status', sa.String(50), default='pending'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now()),
    )
    
    # Create indexes for profile_browser_history
    op.create_index('idx_profile_url_time', 'profile_browser_history', ['profile_id', 'url', 'visit_time'])
    op.create_index('idx_profile_visit_time', 'profile_browser_history', ['profile_id', 'visit_time'])
    op.create_index('idx_profile_history_sync', 'profile_browser_history', ['profile_id', 'sync_status'])
    
    # Create profile_configurations table
    op.create_table('profile_configurations',
        sa.Column('id', sa.Integer(), primary_key=True, index=True),
        sa.Column('profile_id', sa.Integer(), sa.ForeignKey('profiles.id'), nullable=False, index=True),
        sa.Column('preferences', sa.JSON(), nullable=True),
        sa.Column('extensions', sa.JSON(), nullable=True),
        sa.Column('bookmarks', sa.JSON(), nullable=True),
        sa.Column('certificates', sa.JSON(), nullable=True),
        sa.Column('security_settings', sa.JSON(), nullable=True),
        sa.Column('autofill_data', sa.JSON(), nullable=True),
        sa.Column('sync_version', sa.Integer(), default=1),
        sa.Column('last_modified', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('checksum', sa.String(64), nullable=True),
        sa.Column('sync_status', sa.String(50), default='pending'),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now()),
    )
    
    # Create index for profile_configurations
    op.create_index('idx_profile_config_sync', 'profile_configurations', ['profile_id', 'sync_status'])
    
    # Create profile_sync_logs table
    op.create_table('profile_sync_logs',
        sa.Column('id', sa.Integer(), primary_key=True, index=True),
        sa.Column('profile_id', sa.Integer(), sa.ForeignKey('profiles.id'), nullable=False, index=True),
        sa.Column('sync_type', sa.String(50), nullable=False),
        sa.Column('sync_direction', sa.String(50), nullable=False),
        sa.Column('started_at', sa.DateTime(), nullable=False),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('status', sa.String(50), nullable=False, default='pending'),
        sa.Column('items_synced', sa.Integer(), default=0),
        sa.Column('data_size_bytes', sa.BigInteger(), default=0),
        sa.Column('conflicts_resolved', sa.Integer(), default=0),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_details', sa.JSON(), nullable=True),
        sa.Column('initiated_by_user_id', sa.Integer(), nullable=True),
        sa.Column('user_ip_address', sa.String(45), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now()),
    )
    
    # Create indexes for profile_sync_logs
    op.create_index('idx_profile_sync_status', 'profile_sync_logs', ['profile_id', 'status'])
    op.create_index('idx_sync_time', 'profile_sync_logs', ['started_at', 'completed_at'])
    op.create_index('idx_sync_user', 'profile_sync_logs', ['initiated_by_user_id', 'started_at'])


def downgrade():
    # Drop tables in reverse order
    op.drop_table('profile_sync_logs')
    op.drop_table('profile_configurations')
    op.drop_table('profile_browser_history')
    op.drop_table('profile_indexeddb')
    op.drop_table('profile_local_storage')
    
    # Remove columns from profiles table
    op.drop_index('idx_profiles_account_id', 'profiles')
    op.drop_column('profiles', 'last_sync_timestamp')
    op.drop_column('profiles', 'sync_version')
    op.drop_column('profiles', 'shared_by_admin_id')
    op.drop_column('profiles', 'is_shared')
    op.drop_column('profiles', 'account_id')
