"""
Facebook Automation Desktop - Backend API Server (Desktop Version)
Main FastAPI application entry point with authentication bypass for desktop development
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager

# Import our modules
from app.core.config import settings
from app.core.database import init_db
from app.api.routes import profiles_desktop, scraping_desktop, messaging_desktop, system
from app.core.logger import setup_logger

# Setup logger
logger = setup_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Facebook Automation Backend (Desktop Version)...")
    
    # Initialize database
    await init_db()
    logger.info("Database initialized")
    
    logger.info("Authentication bypassed for desktop development")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Facebook Automation Backend...")


# Create FastAPI app
app = FastAPI(
    title="Facebook Automation API (Desktop)",
    description="Backend API for Facebook automation desktop application (Development Mode)",
    version="1.0.0-desktop",
    lifespan=lifespan
)

# Add CORS middleware (more permissive for desktop)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes (without auth middleware)
app.include_router(profiles_desktop.router, prefix="/api/profiles", tags=["profiles"])
app.include_router(scraping_desktop.router, prefix="/api/scraping", tags=["scraping"])
app.include_router(messaging_desktop.router, prefix="/api/messaging", tags=["messaging"])
app.include_router(system.router, prefix="/api/system", tags=["system"])

# Add the /profiles/my-profiles endpoint at root level
app.include_router(profiles_desktop.router, prefix="/profiles", tags=["profiles-root"])

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Facebook Automation Backend (Desktop) is running"}


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Facebook Automation Backend API (Desktop Version)",
        "version": "1.0.0-desktop",
        "mode": "development",
        "docs": "/docs"
    }


if __name__ == "__main__":
    import uvicorn
    
    # Run the server
    uvicorn.run(
        "main_desktop:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
