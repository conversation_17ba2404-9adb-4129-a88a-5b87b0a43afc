"""
Comprehensive Test Suite for Profile Sharing System
Tests profile sharing, synchronization, access control, and security features
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import Test<PERSON>lient
from httpx import Async<PERSON><PERSON>

from app.main import app
from app.core.database import get_db
from app.models.profile import Profile
from app.services.profile_sync_service import ProfileSyncService
from app.services.profile_access_control import ProfileAccessControl
from app.services.profile_security import profile_encryption, audit_logger
from app.services.profile_performance import profile_performance_optimizer


class TestProfileSharingBase:
    """Base test class with common fixtures and utilities"""
    
    @pytest.fixture
    async def db_session(self):
        """Mock database session"""
        session = AsyncMock(spec=AsyncSession)
        return session
    
    @pytest.fixture
    def test_profile_data(self):
        """Sample profile data for testing"""
        return {
            "id": 1,
            "name": "test_profile",
            "profile_path": "/tmp/test_profile",
            "proxy_type": "no_proxy",
            "fingerprint_data": {"user_agent": "test_agent"},
            "is_shared": True,
            "account_id": 123
        }
    
    @pytest.fixture
    def test_user_data(self):
        """Sample user data for testing"""
        return {
            "id": 1,
            "username": "test_user",
            "role": "user",
            "email": "<EMAIL>"
        }
    
    @pytest.fixture
    def test_admin_data(self):
        """Sample admin data for testing"""
        return {
            "id": 2,
            "username": "admin_user",
            "role": "admin",
            "email": "<EMAIL>"
        }


class TestProfileSyncService(TestProfileSharingBase):
    """Test profile synchronization service"""
    
    @pytest.fixture
    def sync_service(self):
        return ProfileSyncService()
    
    @pytest.mark.asyncio
    async def test_capture_profile_data(self, sync_service, db_session, test_profile_data):
        """Test profile data capture"""
        profile_id = test_profile_data["id"]
        profile_path = test_profile_data["profile_path"]
        
        # Mock file system operations
        with patch('os.path.exists', return_value=True), \
             patch('builtins.open', mock_open(read_data='{"test": "data"}')):
            
            result = await sync_service.capture_profile_data(
                profile_id, profile_path, 1, db_session
            )
            
            assert result["success"] is True
            assert "items_captured" in result
            assert result["items_captured"] >= 0
    
    @pytest.mark.asyncio
    async def test_restore_profile_data(self, sync_service, db_session, test_profile_data):
        """Test profile data restoration"""
        profile_id = test_profile_data["id"]
        profile_path = test_profile_data["profile_path"]
        
        # Mock database query
        db_session.execute.return_value.scalar_one_or_none.return_value = Mock(
            local_storage_data=[],
            indexeddb_data=[],
            browser_history=[]
        )
        
        result = await sync_service.restore_profile_data(
            profile_id, profile_path, db_session
        )
        
        assert result["success"] is True
        assert "items_restored" in result
    
    @pytest.mark.asyncio
    async def test_sync_profile_incremental(self, sync_service, db_session, test_profile_data):
        """Test incremental profile synchronization"""
        profile_id = test_profile_data["id"]
        
        with patch.object(sync_service, 'capture_profile_data') as mock_capture, \
             patch.object(sync_service, 'restore_profile_data') as mock_restore:
            
            mock_capture.return_value = {"success": True, "items_captured": 5}
            mock_restore.return_value = {"success": True, "items_restored": 3}
            
            result = await sync_service.sync_profile_incremental(
                profile_id, 1, db_session
            )
            
            assert result["success"] is True
            assert "items_synced" in result


class TestProfileAccessControl(TestProfileSharingBase):
    """Test profile access control system"""
    
    @pytest.fixture
    def access_control(self):
        return ProfileAccessControl()
    
    @pytest.mark.asyncio
    async def test_check_profile_access_owner(self, access_control, db_session, test_profile_data, test_user_data):
        """Test profile access check for owner"""
        user_id = test_user_data["id"]
        profile_id = test_profile_data["id"]
        
        # Mock profile owned by user
        mock_profile = Mock()
        mock_profile.id = profile_id
        mock_profile.account_id = 123
        
        db_session.execute.return_value.scalar_one_or_none.return_value = mock_profile
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"data": {"user_id": user_id}}
            mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
            
            result = await access_control.check_profile_access(user_id, profile_id)
            
            assert result["hasAccess"] is True
            assert result["accessType"] == "owner"
    
    @pytest.mark.asyncio
    async def test_check_profile_access_shared(self, access_control, db_session, test_profile_data, test_user_data):
        """Test profile access check for shared user"""
        user_id = test_user_data["id"]
        profile_id = test_profile_data["id"]
        
        # Mock profile not owned by user but shared
        mock_profile = Mock()
        mock_profile.id = profile_id
        mock_profile.account_id = 456  # Different account
        
        db_session.execute.return_value.scalar_one_or_none.return_value = mock_profile
        
        with patch('httpx.AsyncClient') as mock_client:
            # Mock account owner check (not owner)
            mock_response1 = Mock()
            mock_response1.status_code = 200
            mock_response1.json.return_value = {"data": {"user_id": 999}}
            
            # Mock shared access check (has access)
            mock_response2 = Mock()
            mock_response2.status_code = 200
            mock_response2.json.return_value = {
                "data": [{
                    "shared_with_user_id": user_id,
                    "status": "active",
                    "permissions": {"can_view_profile": True}
                }]
            }
            
            mock_client.return_value.__aenter__.return_value.get.side_effect = [
                mock_response1, mock_response2
            ]
            
            result = await access_control.check_profile_access(user_id, profile_id)
            
            assert result["hasAccess"] is True
            assert result["accessType"] == "shared"
    
    @pytest.mark.asyncio
    async def test_check_profile_access_denied(self, access_control, db_session, test_profile_data, test_user_data):
        """Test profile access denied"""
        user_id = test_user_data["id"]
        profile_id = test_profile_data["id"]
        
        # Mock profile not accessible
        db_session.execute.return_value.scalar_one_or_none.return_value = None
        
        result = await access_control.check_profile_access(user_id, profile_id)
        
        assert result["hasAccess"] is False
        assert result["reason"] == "Profile not found"


class TestProfileSecurity(TestProfileSharingBase):
    """Test profile security features"""
    
    @pytest.mark.asyncio
    async def test_encrypt_decrypt_data(self, test_profile_data):
        """Test data encryption and decryption"""
        profile_id = test_profile_data["id"]
        test_data = "sensitive profile data"
        
        # Encrypt data
        encrypted_data, salt = profile_encryption.encrypt_data(test_data, profile_id)
        
        assert encrypted_data != test_data
        assert salt is not None
        
        # Decrypt data
        decrypted_data = profile_encryption.decrypt_data(encrypted_data, profile_id, salt)
        
        assert decrypted_data == test_data
    
    @pytest.mark.asyncio
    async def test_encrypt_sensitive_fields(self, test_profile_data):
        """Test sensitive field encryption"""
        profile_id = test_profile_data["id"]
        profile_data = {
            "name": "test_profile",
            "password": "secret123",
            "cookies_data": {"session": "abc123"},
            "public_field": "not_encrypted"
        }
        
        encrypted_data = profile_encryption.encrypt_sensitive_fields(profile_data, profile_id)
        
        # Sensitive fields should be encrypted
        assert encrypted_data["password"] != "secret123"
        assert encrypted_data["cookies_data"] != {"session": "abc123"}
        
        # Public fields should remain unchanged
        assert encrypted_data["public_field"] == "not_encrypted"
        assert encrypted_data["name"] == "test_profile"
        
        # Should have encryption salt
        assert "_encryption_salt" in encrypted_data
    
    @pytest.mark.asyncio
    async def test_audit_logging(self, db_session, test_user_data, test_profile_data):
        """Test audit logging functionality"""
        user_id = test_user_data["id"]
        profile_id = test_profile_data["id"]
        
        await audit_logger.log_profile_access(
            db=db_session,
            user_id=user_id,
            profile_id=profile_id,
            action="view",
            details={"test": "data"},
            ip_address="127.0.0.1",
            user_agent="test_agent",
            success=True
        )
        
        # Verify logging was called (would check database in real implementation)
        assert True  # Placeholder assertion


class TestProfilePerformance(TestProfileSharingBase):
    """Test profile performance optimization"""
    
    @pytest.mark.asyncio
    async def test_performance_optimizer_initialization(self):
        """Test performance optimizer initialization"""
        with patch.object(profile_performance_optimizer.cache, 'initialize') as mock_init:
            await profile_performance_optimizer.initialize()
            mock_init.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_optimized_profile_data_cache_hit(self, db_session, test_profile_data):
        """Test optimized profile data retrieval with cache hit"""
        profile_id = test_profile_data["id"]
        cached_data = {"cached": True, "data": "test"}
        
        with patch.object(profile_performance_optimizer.cache, 'get_profile_data', return_value=cached_data):
            result = await profile_performance_optimizer.get_optimized_profile_data(
                profile_id, db_session
            )
            
            assert result == cached_data
    
    @pytest.mark.asyncio
    async def test_optimized_profile_data_cache_miss(self, db_session, test_profile_data):
        """Test optimized profile data retrieval with cache miss"""
        profile_id = test_profile_data["id"]
        
        with patch.object(profile_performance_optimizer.cache, 'get_profile_data', return_value=None), \
             patch.object(profile_performance_optimizer.cache, 'set_profile_data', return_value=True):
            
            result = await profile_performance_optimizer.get_optimized_profile_data(
                profile_id, db_session
            )
            
            assert result is not None
            assert result["id"] == profile_id


class TestProfileSharingAPI(TestProfileSharingBase):
    """Test profile sharing API endpoints"""
    
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self, test_user_data):
        """Mock authentication headers"""
        return {"Authorization": "Bearer test_token"}
    
    def test_get_my_profiles(self, client, auth_headers):
        """Test getting user's accessible profiles"""
        with patch('app.middleware.auth.require_auth') as mock_auth:
            mock_auth.return_value = Mock(id=1, role="user")
            
            response = client.get("/api/profiles/my-profiles", headers=auth_headers)
            
            # Would assert response content in real implementation
            assert response.status_code in [200, 401, 403]  # Depending on auth setup
    
    def test_share_profile_admin_only(self, client, auth_headers):
        """Test profile sharing (admin only)"""
        share_data = {
            "profile_id": 1,
            "shared_with_user_id": 2,
            "permissions": {
                "can_view_profile": True,
                "can_modify_profile": False
            }
        }
        
        with patch('app.middleware.auth.require_admin') as mock_auth:
            mock_auth.return_value = Mock(id=1, role="admin")
            
            response = client.post(
                "/api/profile-sharing/share",
                json=share_data,
                headers=auth_headers
            )
            
            # Would assert response content in real implementation
            assert response.status_code in [200, 401, 403]


class TestIntegrationScenarios(TestProfileSharingBase):
    """Integration tests for complete workflows"""
    
    @pytest.mark.asyncio
    async def test_complete_profile_sharing_workflow(self, db_session):
        """Test complete profile sharing workflow"""
        # This would test the entire flow:
        # 1. Admin creates profile
        # 2. Admin shares profile with user
        # 3. User accesses shared profile
        # 4. Profile data is synchronized
        # 5. Audit logs are created
        # 6. Performance metrics are recorded
        
        # Mock all necessary components
        sync_service = ProfileSyncService()
        access_control = ProfileAccessControl()
        
        # Test profile creation and sharing
        profile_id = 1
        admin_user_id = 1
        regular_user_id = 2
        
        # Mock successful operations
        with patch.object(sync_service, 'capture_profile_data') as mock_capture, \
             patch.object(access_control, 'check_profile_access') as mock_access:
            
            mock_capture.return_value = {"success": True, "items_captured": 10}
            mock_access.return_value = {
                "hasAccess": True,
                "accessType": "shared",
                "permissions": {"can_view_profile": True}
            }
            
            # Test the workflow
            capture_result = await sync_service.capture_profile_data(
                profile_id, "/tmp/test", admin_user_id, db_session
            )
            
            access_result = await access_control.check_profile_access(
                regular_user_id, profile_id
            )
            
            assert capture_result["success"] is True
            assert access_result["hasAccess"] is True
    
    @pytest.mark.asyncio
    async def test_security_and_performance_integration(self, db_session, test_profile_data):
        """Test integration between security and performance features"""
        profile_id = test_profile_data["id"]
        sensitive_data = {
            "password": "secret123",
            "cookies": {"session": "abc123"}
        }
        
        # Test encryption with performance monitoring
        with patch.object(profile_performance_optimizer.monitor, 'record_profile_operation') as mock_monitor:
            
            # Encrypt data
            encrypted_data = profile_encryption.encrypt_sensitive_fields(
                sensitive_data, profile_id
            )
            
            # Verify encryption worked
            assert encrypted_data["password"] != "secret123"
            
            # Test performance optimization
            result = await profile_performance_optimizer.get_optimized_profile_data(
                profile_id, db_session
            )
            
            assert result is not None


# Test configuration and fixtures
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(autouse=True)
async def setup_test_environment():
    """Setup test environment before each test"""
    # Initialize test database, clear caches, etc.
    pass


@pytest.fixture(autouse=True)
async def cleanup_test_environment():
    """Cleanup test environment after each test"""
    # Clean up test data, reset mocks, etc.
    yield
    # Cleanup code here


# Test utilities
def mock_open(read_data=""):
    """Utility function to mock file operations"""
    from unittest.mock import mock_open as _mock_open
    return _mock_open(read_data=read_data)


def create_test_profile(profile_id=1, **kwargs):
    """Utility function to create test profile data"""
    default_data = {
        "id": profile_id,
        "name": f"test_profile_{profile_id}",
        "profile_path": f"/tmp/test_profile_{profile_id}",
        "proxy_type": "no_proxy",
        "is_shared": True,
        "account_id": 123
    }
    default_data.update(kwargs)
    return default_data


def create_test_user(user_id=1, role="user", **kwargs):
    """Utility function to create test user data"""
    default_data = {
        "id": user_id,
        "username": f"test_user_{user_id}",
        "role": role,
        "email": f"test{user_id}@example.com"
    }
    default_data.update(kwargs)
    return default_data


# Performance test markers
pytestmark = [
    pytest.mark.asyncio,
    pytest.mark.timeout(30)  # 30 second timeout for all tests
]
