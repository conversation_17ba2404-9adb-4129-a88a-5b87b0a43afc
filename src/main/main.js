/**
 * Facebook Automation Desktop - Main Electron Process
 */

const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const Store = require('electron-store');

// Initialize electron store for settings
const store = new Store();

// Keep a global reference of the window object
let mainWindow;
let backendProcess;

// Backend server configuration
const BACKEND_PORT = 8000;
const BACKEND_HOST = '127.0.0.1';

/**
 * Create the main application window
 */
function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    show: false, // Don't show until ready
    titleBarStyle: 'default'
  });

  // Load the app - always load from built files for simplicity
  const distPath = path.join(__dirname, '../../frontend/dist/index.html');
  console.log('Loading frontend from:', distPath);

  // Check if file exists
  const fs = require('fs');
  if (fs.existsSync(distPath)) {
    console.log('✅ Frontend dist file exists');
  } else {
    console.error('❌ Frontend dist file NOT found:', distPath);
  }

  mainWindow.loadFile(distPath);

  // Always open dev tools for debugging
  mainWindow.webContents.openDevTools();

  // Add more detailed logging
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ Frontend loaded successfully');
  });

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ Frontend failed to load:', errorCode, errorDescription);
  });

  mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    console.log(`[RENDERER ${level}] ${message} (${sourceId}:${line})`);
  });

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus on window
    if (isDev) {
      mainWindow.focus();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

/**
 * Check if backend is already running
 */
async function checkBackendRunning() {
  try {
    const response = await fetch(`http://${BACKEND_HOST}:${BACKEND_PORT}/docs`);
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * Start the Python backend server
 */
function startBackend() {
  return new Promise((resolve, reject) => {
    const backendPath = path.join(__dirname, '../../backend');
    const venvPath = path.join(backendPath, 'venv');
    const pythonExecutable = process.platform === 'win32'
      ? path.join(venvPath, 'Scripts', 'python.exe')
      : path.join(venvPath, 'bin', 'python');

    console.log('Starting backend server...');
    console.log('Using Python:', pythonExecutable);

    backendProcess = spawn(pythonExecutable, ['-m', 'uvicorn', 'main:app', '--host', BACKEND_HOST, '--port', BACKEND_PORT.toString()], {
      cwd: backendPath,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    backendProcess.stdout.on('data', (data) => {
      console.log(`Backend stdout: ${data}`);
      if (data.toString().includes('Uvicorn running') || data.toString().includes('Application startup complete')) {
        resolve();
      }
    });

    backendProcess.stderr.on('data', (data) => {
      console.error(`Backend stderr: ${data}`);
      if (data.toString().includes('Uvicorn running') || data.toString().includes('Application startup complete')) {
        resolve();
      }
    });

    backendProcess.on('close', (code) => {
      console.log(`Backend process exited with code ${code}`);
      if (code !== 0) {
        reject(new Error(`Backend process exited with code ${code}`));
      }
    });

    backendProcess.on('error', (error) => {
      console.error('Failed to start backend:', error);
      reject(error);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      reject(new Error('Backend startup timeout'));
    }, 30000);
  });
}

/**
 * Ensure backend is running (start if needed)
 */
async function ensureBackend() {
  const isRunning = await checkBackendRunning();

  if (isRunning) {
    console.log('Backend is already running, connecting to existing instance...');
    return;
  }

  console.log('Backend not running, starting new instance...');
  return startBackend();
}

/**
 * Stop the Python backend server
 */
function stopBackend() {
  if (backendProcess) {
    console.log('Stopping backend server...');
    backendProcess.kill();
    backendProcess = null;
  }
}

// App event handlers
app.whenReady().then(async () => {
  try {
    // Ensure backend is running
    await ensureBackend();
    console.log('Backend is ready');

    // Then create window
    createWindow();
    
  } catch (error) {
    console.error('Failed to start application:', error);
    
    // Show error dialog
    dialog.showErrorBox(
      'Startup Error',
      `Failed to start the application: ${error.message}`
    );
    
    app.quit();
  }
});

app.on('window-all-closed', () => {
  stopBackend();
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.on('before-quit', () => {
  stopBackend();
});

// IPC handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-backend-url', () => {
  return `http://${BACKEND_HOST}:${BACKEND_PORT}`;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('store-get', (event, key) => {
  return store.get(key);
});

ipcMain.handle('store-set', (event, key, value) => {
  store.set(key, value);
});

ipcMain.handle('store-delete', (event, key) => {
  store.delete(key);
});
