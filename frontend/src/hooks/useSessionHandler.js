/**
 * Session Handler Hook - Handles session expiry and automatic logout
 */

import { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext.simple';

const useSessionHandler = () => {
  const { isAuthenticated, logout, checkAuthStatus } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) return;

    // Set up periodic session validation
    const sessionCheckInterval = setInterval(async () => {
      console.log('🔍 [SESSION] Performing periodic session validation...');
      
      try {
        await checkAuthStatus();
      } catch (error) {
        console.error('❌ [SESSION] Session validation failed:', error);
        // Session is invalid, logout user
        await logout();
      }
    }, 10 * 60 * 1000); // Check every 10 minutes

    // Set up activity-based session refresh
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    let lastActivity = Date.now();
    let activityCheckInterval;

    const updateActivity = () => {
      lastActivity = Date.now();
    };

    const checkActivity = () => {
      const now = Date.now();
      const timeSinceLastActivity = now - lastActivity;
      
      // If user has been inactive for more than 30 minutes, check session
      if (timeSinceLastActivity > 30 * 60 * 1000) {
        console.log('🔍 [SESSION] User inactive for 30+ minutes, checking session...');
        checkAuthStatus();
      }
    };

    // Add activity listeners
    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    // Check activity every 5 minutes
    activityCheckInterval = setInterval(checkActivity, 5 * 60 * 1000);

    // Cleanup
    return () => {
      clearInterval(sessionCheckInterval);
      clearInterval(activityCheckInterval);
      
      activityEvents.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, [isAuthenticated, logout, checkAuthStatus]);

  // Handle API response errors (401 Unauthorized)
  const handleApiError = async (error) => {
    if (error.response && error.response.status === 401) {
      console.log('❌ [SESSION] Received 401 Unauthorized, logging out...');
      await logout();
      return true; // Indicates that logout was triggered
    }
    return false;
  };

  return {
    handleApiError
  };
};

export default useSessionHandler;
