/**
 * Login Modal Component - Handles user authentication
 */

import React, { useState } from 'react';
import { Modal, Form, Input, Button, Alert, Typography, Divider } from 'antd';
import { UserOutlined, LockOutlined, GoogleOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text } = Typography;

const LoginModal = ({ visible, onCancel }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { login } = useAuth();

  const handleLogin = async (values) => {
    setLoading(true);
    setError('');

    try {
      console.log('🔍 [LOGIN] Attempting login with:', values.email);

      // Call auto-login service
      const response = await fetch('http://localhost:3000/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: values.email,
          password: values.password,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ [LOGIN] Login successful:', data);

        // Update auth context
        await login({
          id: data.user?.id || 1,
          email: values.email,
          name: data.user?.name || values.email,
          role: data.user?.role || 'user',
          has_bot_insta_access: data.user?.has_bot_insta_access || true,
        });

        // Close modal
        onCancel();
        form.resetFields();
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Login failed. Please check your credentials.');
      }
    } catch (error) {
      console.error('❌ [LOGIN] Login error:', error);
      setError('Connection failed. Please make sure the auto-login service is running.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = () => {
    // Redirect to Google OAuth
    window.location.href = 'http://localhost:3000/auth/google';
  };

  const fillTestCredentials = () => {
    form.setFieldsValue({
      email: '<EMAIL>',
      password: 'desktop123',
    });
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={400}
      centered
    >
      <div style={{ padding: '20px 0' }}>
        <Title level={3} style={{ textAlign: 'center', marginBottom: 30 }}>
          Login to Facebook Automation
        </Title>

        {error && (
          <Alert
            message={error}
            type="error"
            style={{ marginBottom: 20 }}
            closable
            onClose={() => setError('')}
          />
        )}

        <Form
          form={form}
          onFinish={handleLogin}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Please input your email!' },
              { type: 'email', message: 'Please enter a valid email!' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="Enter your email"
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[
              { required: true, message: 'Please input your password!' },
              { min: 6, message: 'Password must be at least 6 characters!' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="Enter your password"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{ height: 45 }}
            >
              {loading ? 'Logging in...' : 'Login'}
            </Button>
          </Form.Item>
        </Form>

        <Divider>or</Divider>

        <Button
          icon={<GoogleOutlined />}
          onClick={handleGoogleLogin}
          block
          style={{ height: 45, marginBottom: 15 }}
        >
          Continue with Google
        </Button>

        <div style={{ textAlign: 'center', marginTop: 20 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            For desktop development, use test credentials:
          </Text>
          <br />
          <Button
            type="link"
            size="small"
            onClick={fillTestCredentials}
            style={{ padding: 0, height: 'auto', fontSize: '12px' }}
          >
            <EMAIL> / desktop123
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default LoginModal;
