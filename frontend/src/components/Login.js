/**
 * Login Component - Google OAuth Login
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Alert, Spin } from 'antd';
import { GoogleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext.simple';

const { Title, Text } = Typography;

// Auto-login service URL (should match your NestJS service)
const AUTO_LOGIN_SERVICE_URL = 'http://localhost:3000';

const Login = ({ onLoginSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [loginStatus, setLoginStatus] = useState(null);
  const { login } = useAuth();

  useEffect(() => {
    // Listen for messages from popup window
    const handleMessage = async (event) => {
      console.log('🔍 [LOGIN] Received message from popup:', event);
      console.log('🔍 [LOGIN] Event origin:', event.origin);
      console.log('🔍 [LOGIN] Expected origin:', AUTO_LOGIN_SERVICE_URL);

      if (event.origin !== AUTO_LOGIN_SERVICE_URL) {
        console.log('❌ [LOGIN] Origin mismatch, ignoring message');
        return;
      }

      console.log('🔍 [LOGIN] Message data:', event.data);

      if (event.data.type === 'AUTH_SUCCESS') {
        console.log('✅ [LOGIN] Received AUTH_SUCCESS, verifying token...');
        setLoading(true);
        setError(null);

        try {
          // Wait a bit for cookies to be set
          await new Promise(resolve => setTimeout(resolve, 500));

          console.log('🔍 [LOGIN] Making verify request to backend API');

          // Verify authentication with backend API (which proxies to auto-login service)
          const response = await fetch('/api/auth/verify-cookies', {
            method: 'GET',
            credentials: 'include', // Include cookies
          });

          console.log('🔍 [LOGIN] Verify response status:', response.status);
          console.log('🔍 [LOGIN] Verify response headers:', response.headers);

          if (response.ok) {
            const userData = await response.json();
            console.log('✅ [LOGIN] User data received:', userData);

            // Use AuthContext to handle login
            await login(userData.user);

            setLoginStatus({
              success: true,
              user: userData.user,
              message: 'Login successful!'
            });

            // Call the success callback if provided
            if (onLoginSuccess) {
              console.log('✅ [LOGIN] Calling onLoginSuccess callback');
              onLoginSuccess(userData.user);
            }

            // The ProtectedRoute will automatically redirect to dashboard
            // since the user is now authenticated via AuthContext
          } else {
            const errorText = await response.text();
            console.error('❌ [LOGIN] Verify failed:', response.status, errorText);
            throw new Error(`Failed to verify authentication: ${response.status} ${errorText}`);
          }
        } catch (err) {
          console.error('❌ [LOGIN] Authentication verification failed:', err);
          setError(`Authentication verification failed: ${err.message}. Please try again.`);
        } finally {
          setLoading(false);
        }
      } else if (event.data.type === 'AUTH_ERROR') {
        console.error('❌ [LOGIN] Received AUTH_ERROR:', event.data.message);
        setLoading(false);
        setError(event.data.message || 'Authentication failed');
      }
    };

    console.log('🔍 [LOGIN] Setting up message listener');
    window.addEventListener('message', handleMessage);

    return () => {
      console.log('🔍 [LOGIN] Removing message listener');
      window.removeEventListener('message', handleMessage);
    };
  }, [onLoginSuccess]);

  const handleGoogleLogin = () => {
    console.log('🔍 [LOGIN] Starting Google login process');
    setLoading(true);
    setError(null);
    setLoginStatus(null);

    // Open popup window for Google OAuth
    const popupUrl = `${AUTO_LOGIN_SERVICE_URL}/auth/google`;
    console.log('🔍 [LOGIN] Opening popup to:', popupUrl);

    const popup = window.open(
      popupUrl,
      'google-login',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    );

    // Check if popup was blocked
    if (!popup) {
      console.error('❌ [LOGIN] Popup was blocked');
      setLoading(false);
      setError('Popup was blocked. Please allow popups for this site and try again.');
      return;
    }

    console.log('✅ [LOGIN] Popup opened successfully');

    // Monitor popup
    const checkClosed = setInterval(() => {
      if (popup.closed) {
        console.log('🔍 [LOGIN] Popup window closed');
        clearInterval(checkClosed);

        // Only set error if we haven't received a success message
        setTimeout(() => {
          if (loading && !loginStatus) {
            console.log('❌ [LOGIN] Popup closed without success message');
            setLoading(false);
            setError('Login was cancelled or failed');
          }
        }, 1000);
      }
    }, 1000);
  };

  if (loginStatus && loginStatus.success) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Card
          style={{
            width: 400,
            textAlign: 'center',
            borderRadius: 16,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}
        >
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
            <Title level={3} style={{ margin: 0, color: '#52c41a' }}>
              Login Successful!
            </Title>
            <Text type="secondary">
              Welcome back, {loginStatus.user.email}
            </Text>
            <Spin size="small" />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Redirecting to dashboard...
            </Text>
          </Space>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Card 
        style={{ 
          width: 400, 
          textAlign: 'center',
          borderRadius: 16,
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={2} style={{ margin: 0, color: '#667eea' }}>
              Facebook Automation
            </Title>
            <Text type="secondary">
              Sign in to access your dashboard
            </Text>
          </div>

          {error && (
            <Alert
              message="Login Error"
              description={error}
              type="error"
              icon={<ExclamationCircleOutlined />}
              showIcon
              closable
              onClose={() => setError(null)}
            />
          )}

          <Button
            type="primary"
            size="large"
            icon={<GoogleOutlined />}
            onClick={handleGoogleLogin}
            loading={loading}
            style={{
              width: '100%',
              height: 48,
              fontSize: 16,
              background: '#4285f4',
              borderColor: '#4285f4',
              borderRadius: 8,
            }}
          >
            {loading ? 'Signing in...' : 'Sign in with Google'}
          </Button>

          <div style={{ marginTop: 24 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              By signing in, you agree to our Terms of Service and Privacy Policy.
              <br />
              You need an active account with bot Instagram package to access the features.
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default Login;
