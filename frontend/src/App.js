/**
 * Facebook Automation Desktop - Main React App Component
 */

import React from 'react';
import { ConfigProvider } from 'antd';

// Import the modern layout
import ModernLayout from './components/ModernLayout';
import ProtectedRoute from './components/ProtectedRoute';
import LoginModal from './components/LoginModal';
import { AuthProvider, useAuth } from './contexts/AuthContext';

// Import global styles
import './styles/App.css';

// Ant Design theme configuration
const themeConfig = {
  token: {
    colorPrimary: '#667eea',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    borderRadius: 8,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Input: {
      borderRadius: 8,
      controlHeight: 40,
    },
    Card: {
      borderRadius: 16,
    },
    Menu: {
      borderRadius: 8,
    },
  },
};

// Main App Content Component
const AppContent = () => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '16px'
      }}>
        Loading...
      </div>
    );
  }

  return (
    <div className="App">
      {isAuthenticated ? (
        <ProtectedRoute requirePackageAccess={true}>
          <ModernLayout />
        </ProtectedRoute>
      ) : (
        <LoginModal visible={true} onCancel={() => {}} />
      )}
    </div>
  );
};

function App() {
  console.log('🚀 [APP] App component rendering...');

  try {
    return (
      <ConfigProvider theme={themeConfig}>
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </ConfigProvider>
    );
  } catch (error) {
    console.error('❌ [APP] Error in App component:', error);
    return (
      <div style={{ padding: '20px', color: 'red', fontFamily: 'monospace' }}>
        <h2>App Component Error</h2>
        <p>Error: {error.message}</p>
        <p>Check console for details</p>
      </div>
    );
  }
}

export default App;
