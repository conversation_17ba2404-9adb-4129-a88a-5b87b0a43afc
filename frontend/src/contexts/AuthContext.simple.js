/**
 * Simple Authentication Context - Bypasses authentication for desktop app
 */

import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  console.log('🚀 [AUTH] AuthProvider component rendering...');

  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [hasValidPackage, setHasValidPackage] = useState(true);

  // Auto-authenticate for desktop app
  useEffect(() => {
    console.log('🚀 [AUTH] useEffect triggered');

    const initAuth = () => {
      console.log('🔍 [AUTH] Initializing simple auth for desktop app');

      try {
        // Create a mock user for desktop app
        const mockUser = {
          id: 1,
          email: '<EMAIL>',
          name: 'Desktop User',
          role: 'admin',
          status: 'active'
        };

        console.log('🔍 [AUTH] Setting mock user:', mockUser);
        setUser(mockUser);
        setIsAuthenticated(true);
        setHasValidPackage(true);
        setLoading(false);

        console.log('✅ [AUTH] Desktop auth initialized successfully');
      } catch (error) {
        console.error('❌ [AUTH] Error initializing auth:', error);
        setLoading(false);
      }
    };

    // Small delay to simulate loading
    console.log('🔍 [AUTH] Setting timeout for auth init...');
    setTimeout(initAuth, 500);
  }, []);

  const login = async (userData) => {
    setUser(userData);
    setIsAuthenticated(true);
    setHasValidPackage(true);
  };

  const logout = async () => {
    console.log('🔍 [AUTH] Logging out desktop user');
    setUser(null);
    setIsAuthenticated(false);
    setHasValidPackage(false);
  };

  const checkAuthStatus = async () => {
    // Always return success for desktop app
    return Promise.resolve();
  };

  const checkPackageAccess = async () => {
    // Always return success for desktop app
    return Promise.resolve();
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    hasValidPackage,
    login,
    logout,
    checkAuthStatus,
    checkPackageAccess,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
