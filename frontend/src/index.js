/**
 * Facebook Automation Desktop - React App Entry Point
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';
import 'antd/dist/reset.css';
import './styles/global.css';

// Add global error handlers
window.addEventListener('error', (event) => {
  console.error('❌ [GLOBAL ERROR]', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('❌ [UNHANDLED PROMISE REJECTION]', event.reason);
});

// Debug logging
console.log('🚀 [INIT] Starting React app...');
console.log('🚀 [INIT] Environment:', process.env.NODE_ENV);
console.log('🚀 [INIT] Electron API available:', !!window.electronAPI);

// Get the root element
const container = document.getElementById('root');
if (!container) {
  console.error('❌ [INIT] Root element not found!');
  document.body.innerHTML = '<div style="padding: 20px; color: red; font-family: monospace;">ERROR: Root element not found!</div>';
} else {
  console.log('✅ [INIT] Root element found');

  try {
    const root = createRoot(container);
    console.log('✅ [INIT] React root created');

    // Render the app with error boundary
    root.render(
      <React.StrictMode>
        <ErrorBoundary>
          <App />
        </ErrorBoundary>
      </React.StrictMode>
    );

    console.log('✅ [INIT] App rendered successfully');
  } catch (error) {
    console.error('❌ [INIT] Failed to render app:', error);
    container.innerHTML = `
      <div style="padding: 20px; color: red; font-family: monospace;">
        <h2>React Initialization Error</h2>
        <p>Error: ${error.message}</p>
        <p>Check console for details</p>
      </div>
    `;
  }
}
